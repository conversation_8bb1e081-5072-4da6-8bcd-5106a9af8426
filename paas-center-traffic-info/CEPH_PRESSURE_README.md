# 集群监控数据功能实现（支持多指标扩展）

## 功能概述

本功能实现了集群监控数据的收集、存储和查询，支持多种指标类型和折线图数据展示。主要包括：

1. **数据上报接口**：接收来自多台机器的高频监控数据上报（支持50台机器每秒一次）
2. **双表存储架构**：原始数据表+分钟聚合表，支持超高频写入和高效查询
3. **智能查询策略**：根据时间跨度自动选择最优数据源
4. **多指标支持**：支持Ceph压力、写入速率、容量占用等多种指标
5. **数据生命周期管理**：原始表保留1个月，聚合表保留3个月

## 技术架构

### 数据库设计

#### 1. 原始数据表 `cluster_monitor_raw_data`
- **用途**：存储高频原始监控数据
- **保留期**：1个月
- **主要字段**：
  - `cluster_code`：集群编码
  - `metric_type`：指标类型（ceph_pressure, write_rate, capacity_usage等）
  - `metric_value`：指标值
  - `metric_unit`：指标单位
  - `report_time`：上报时间
- **分区策略**：按月分区，提高查询和清理效率

#### 2. 分钟聚合表 `cluster_monitor_minute_data`
- **用途**：存储分钟维度聚合数据
- **保留期**：3个月
- **主要字段**：
  - `cluster_code`：集群编码
  - `metric_type`：指标类型
  - `minute_time`：分钟时间点
  - `avg_value`：平均值
  - `max_value`：最大值
  - `min_value`：最小值
  - `data_count`：原始数据点数量
- **唯一约束**：(cluster_code, metric_type, minute_time)

### 核心组件

1. **实体类**：
   - `ClusterMonitorRawData`：原始数据实体
   - `ClusterMonitorMinuteData`：分钟聚合数据实体
2. **数据访问层**：`ClusterMonitorDataMapper`（位于paas包下）
3. **服务层**：
   - `ICephPressureDataService`：业务服务接口
   - `IClusterMonitorAggregationService`：数据聚合服务接口
4. **控制器**：`TrafficDataController`（新增接口）
5. **定时任务**：`ClusterMonitorDataTask`（数据聚合和清理）
6. **常量类**：`MetricTypeConstant`（指标类型定义）

## API接口

### 1. 数据上报接口

**接口地址**：`POST /traffic-info/open/traffic/edge/ceph/report`

**请求参数**：
```json
{
  "clusterCode": "001",
  "metrics": {
    "cephPressure": 32.12
  }
}
```

**支持的指标类型**：
- `ceph_pressure`：Ceph压力值（%）
- `write_rate`：写入速率（MB/s）
- `capacity_usage`：容量占用（GB）
- `read_rate`：读取速率（MB/s）
- `iops`：IOPS值
- `latency`：延迟（ms）

**响应结果**：
```json
{
  "code": 200,
  "msg": "请求成功",
  "data": null,
  "ts": 1640995200000
}
```

### 2. 折线图数据查询接口

**接口地址**：`POST /traffic-info/open/traffic/edge/ceph/chart`

**请求参数**：
```json
{
  "clusterCode": "001",
  "startTime": "2025-01-01 00:00:00",
  "endTime": "2025-01-01 23:59:59"
}
```

**响应结果**：
```json
{
  "code": 200,
  "msg": "请求成功",
  "data": [
    {
      "timePoint": "2025-01-01T10:00:00.000+08:00",
      "timeStr": "2025-01-01 10:00:00",
      "avgPressure": 32.5,
      "maxPressure": 35.0,
      "minPressure": 30.0,
      "dataCount": 6
    }
  ],
  "ts": 1640995200000
}
```

## 性能优化

### 1. 双表存储架构
- **原始表**：支持高频写入，保留1个月详细数据
- **聚合表**：预聚合分钟维度数据，保留3个月历史数据
- **分区策略**：两表均按月分区，提高查询和维护效率

### 2. 智能查询策略
```
时间跨度 <= 30分钟：查询原始表，实时聚合
时间跨度 > 30分钟：查询分钟聚合表
数据量 > 1000条：自动进行二次聚合
```

### 3. 数据聚合策略
- **实时聚合**：每分钟定时任务将原始数据聚合到分钟表
- **查询聚合**：当分钟表数据量超过1000时，按时间间隔聚合
  - <= 5分钟：5分钟间隔
  - <= 10分钟：10分钟间隔
  - <= 15分钟：15分钟间隔
  - <= 30分钟：30分钟间隔
  - <= 60分钟：1小时间隔
  - > 60分钟：2小时间隔

### 4. 索引优化
- 原始表：`(cluster_code, metric_type, report_time)`
- 聚合表：`(cluster_code, metric_type, minute_time)`
- 唯一约束：聚合表的 `(cluster_code, metric_type, minute_time)`

## 数据生命周期管理

### 1. 数据聚合任务
- **执行频率**：每分钟执行一次
- **聚合逻辑**：将2分钟前的原始数据聚合到分钟表
- **聚合方式**：计算平均值、最大值、最小值和数据点数量

### 2. 数据清理任务
- **执行时间**：每天凌晨2点
- **清理策略**：
  - 原始表：删除30天前的数据
  - 分钟聚合表：删除90天前的数据

## 部署说明

### 1. 数据库初始化
执行SQL脚本：`sql/ceph_pressure_data.sql`
- 创建原始数据表和分钟聚合表
- 建立分区和索引
- 创建数据聚合存储过程

### 2. 测试验证
运行测试类：`CephPressureDataServiceTest`
- 测试数据保存功能
- 测试短时间范围查询（原始表）
- 测试长时间范围查询（聚合表）
- 测试数据聚合功能
- 测试数据清理功能

## 扩展性考虑

### 1. 高并发写入支持
- **当前能力**：支持50台机器每秒一次的高频写入
- **架构优势**：
  - 原始表专门优化写入性能
  - 分区表减少锁竞争
  - 异步聚合不影响写入性能

### 2. 多指标扩展
- **设计优势**：单表支持多种监控指标
- **新增指标**：只需在 `MetricTypeConstant` 中定义新常量
- **兼容性**：现有查询逻辑自动支持新指标类型

### 3. 存储优化
- **分区策略**：按月分区，支持快速删除过期数据
- **索引优化**：复合索引覆盖主要查询场景
- **数据压缩**：历史数据可启用压缩存储

### 4. 查询性能
- **智能路由**：根据时间跨度自动选择最优数据源
- **预聚合**：分钟表避免实时聚合开销
- **结果控制**：确保返回数据量在1000条左右

## 监控告警

建议添加以下监控指标：
1. **写入性能**：数据写入成功率、写入延迟
2. **查询性能**：查询响应时间、查询成功率
3. **存储状态**：数据库连接池状态、表空间使用率
4. **任务状态**：数据聚合任务执行状态、数据清理任务执行状态
5. **数据质量**：数据完整性检查、聚合数据一致性验证

## 使用示例

### 1. 上报Ceph压力数据
```bash
curl -X POST http://localhost:8080/traffic-info/open/traffic/edge/ceph/report \
  -H "Content-Type: application/json" \
  -d '{
    "clusterCode": "cluster-001",
    "metrics": {
      "cephPressure": 75.5
    }
  }'
```

### 2. 查询折线图数据
```bash
curl -X POST http://localhost:8080/traffic-info/open/traffic/edge/ceph/chart \
  -H "Content-Type: application/json" \
  -d '{
    "clusterCode": "cluster-001",
    "startTime": "2025-01-01 10:00:00",
    "endTime": "2025-01-01 11:00:00"
  }'
```

## 总结

本实现方案通过双表存储架构和智能查询策略，完美解决了高频数据写入和高效查询的矛盾，同时具备良好的扩展性，可以轻松支持更多监控指标和更大的数据规模。
