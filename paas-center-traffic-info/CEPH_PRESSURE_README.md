# Ceph压力数据监控功能实现

## 功能概述

本功能实现了Ceph压力数据的收集、存储和查询，支持折线图数据展示。主要包括：

1. **数据上报接口**：接收来自16台机器每10秒一次的压力数据上报
2. **数据存储**：使用MySQL8.0.28存储压力数据，支持高频写入
3. **数据查询接口**：支持时间区间查询，返回分钟维度数据
4. **数据聚合**：当查询结果超过1000条时，自动聚合到1000条左右
5. **数据清理**：定时清理过期数据，避免数据量过大

## 技术架构

### 数据库设计
- **表名**：`ceph_pressure_data`
- **主要字段**：
  - `cluster_code`：集群编码
  - `ceph_pressure`：压力值（百分比）
  - `report_time`：上报时间
- **索引优化**：针对查询场景建立复合索引

### 核心组件

1. **实体类**：`CephPressureData`
2. **数据访问层**：`CephPressureDataMapper`（位于paas包下）
3. **服务层**：`ICephPressureDataService` 和 `CephPressureDataServiceImpl`
4. **控制器**：`TrafficDataController`（新增接口）
5. **定时任务**：`CephPressureDataCleanTask`

## API接口

### 1. 数据上报接口

**接口地址**：`POST /traffic-info/open/traffic/edge/ceph/report`

**请求参数**：
```json
{
  "clusterCode": "001",
  "metrics": {
    "cephPressure": 32.12
  }
}
```

**响应结果**：
```json
{
  "code": 200,
  "msg": "请求成功",
  "data": null,
  "ts": 1640995200000
}
```

### 2. 折线图数据查询接口

**接口地址**：`POST /traffic-info/open/traffic/edge/ceph/chart`

**请求参数**：
```json
{
  "clusterCode": "001",
  "startTime": "2025-01-01 00:00:00",
  "endTime": "2025-01-01 23:59:59"
}
```

**响应结果**：
```json
{
  "code": 200,
  "msg": "请求成功",
  "data": [
    {
      "timePoint": "2025-01-01T10:00:00.000+08:00",
      "timeStr": "2025-01-01 10:00:00",
      "avgPressure": 32.5,
      "maxPressure": 35.0,
      "minPressure": 30.0,
      "dataCount": 6
    }
  ],
  "ts": 1640995200000
}
```

## 性能优化

### 1. 数据写入优化
- 使用单条插入，适应高频写入场景
- 建立合适的索引，平衡写入和查询性能

### 2. 数据查询优化
- **分钟维度聚合**：默认按分钟聚合数据
- **智能聚合**：当数据量超过1000时，自动计算合适的聚合间隔
- **索引优化**：使用复合索引 `(cluster_code, report_time)`

### 3. 数据聚合策略
```
数据量 <= 1000：返回分钟维度数据
数据量 > 1000：根据时间跨度计算聚合间隔
- <= 5分钟：5分钟间隔
- <= 10分钟：10分钟间隔  
- <= 15分钟：15分钟间隔
- <= 30分钟：30分钟间隔
- <= 60分钟：1小时间隔
- > 60分钟：2小时间隔
```

## 数据清理

### 定时清理任务
- **执行时间**：每天凌晨2点
- **清理策略**：删除30天前的数据（可配置）
- **配置参数**：`ceph.pressure.data.retention.days`

## 部署说明

### 1. 数据库初始化
执行SQL脚本：`sql/ceph_pressure_data.sql`

### 2. 配置参数
```yaml
# 数据保留天数配置
ceph:
  pressure:
    data:
      retention:
        days: 30
```

### 3. 测试验证
运行测试类：`CephPressureDataServiceTest`

## 扩展性考虑

### 1. 高并发写入
- 当前设计支持16台机器每10秒一次的写入频率
- 如需支持更高频率，可考虑：
  - 批量写入优化
  - 分库分表
  - 使用时序数据库

### 2. 数据量增长
- 建立了时间索引，支持快速范围查询
- 可选择启用分区表，按月分区存储
- 定时清理机制避免数据无限增长

### 3. 查询性能
- 智能聚合算法确保返回数据量控制在1000左右
- 复合索引优化查询性能
- 支持按集群编码和时间范围的高效查询

## 监控告警

建议添加以下监控指标：
1. 数据写入成功率
2. 查询响应时间
3. 数据库连接池状态
4. 定时清理任务执行状态
