-- Ceph压力数据表
CREATE TABLE `ceph_pressure_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `ceph_pressure` double NOT NULL COMMENT 'Ceph压力值（百分比）',
  `report_time` datetime NOT NULL COMMENT '上报时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_cluster_code` (`cluster_code`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_cluster_report_time` (`cluster_code`, `report_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Ceph压力数据表';

-- 创建分区表（可选，用于大数据量场景）
-- ALTER TABLE ceph_pressure_data PARTITION BY RANGE (TO_DAYS(report_time)) (
--   PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
--   PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
--   PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
--   PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
--   PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
--   PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
--   PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
--   PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
--   PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
--   PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
--   PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
--   PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
--   PARTITION pmax VALUES LESS THAN MAXVALUE
-- );
