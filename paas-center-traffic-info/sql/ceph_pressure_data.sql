-- 集群监控原始数据表（支持多种指标）
CREATE TABLE `cluster_monitor_raw_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `metric_type` varchar(32) NOT NULL COMMENT '指标类型：ceph_pressure,write_rate,capacity_usage等',
  `metric_value` double NOT NULL COMMENT '指标值',
  `metric_unit` varchar(16) DEFAULT NULL COMMENT '指标单位：%,MB/s,GB等',
  `report_time` datetime NOT NULL COMMENT '上报时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `report_time`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控原始数据表（保留1个月）'
PARTITION BY RANGE (TO_DAYS(report_time)) (
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
  PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
  PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
  PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
  PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
  PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
  PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
  PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
  PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
  PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
  PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
  PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
  PARTITION pmax VALUES LESS THAN MAXVALUE
);

-- 集群监控分钟维度聚合表
CREATE TABLE `cluster_monitor_minute_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `metric_type` varchar(32) NOT NULL COMMENT '指标类型：ceph_pressure,write_rate,capacity_usage等',
  `minute_time` datetime NOT NULL COMMENT '分钟时间点（精确到分钟）',
  `avg_value` double NOT NULL COMMENT '平均值',
  `max_value` double NOT NULL COMMENT '最大值',
  `min_value` double NOT NULL COMMENT '最小值',
  `data_count` int(11) NOT NULL DEFAULT '0' COMMENT '原始数据点数量',
  `metric_unit` varchar(16) DEFAULT NULL COMMENT '指标单位',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cluster_metric_minute` (`cluster_code`, `metric_type`, `minute_time`),
  KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `minute_time`),
  KEY `idx_minute_time` (`minute_time`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控分钟维度聚合表（保留3个月）'
PARTITION BY RANGE (TO_DAYS(minute_time)) (
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
  PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
  PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
  PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
  PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
  PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
  PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
  PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
  PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
  PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
  PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
  PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
  PARTITION pmax VALUES LESS THAN MAXVALUE
);

-- 创建分钟聚合数据的存储过程
DELIMITER $$
CREATE PROCEDURE `AggregateClusterMonitorData`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_cluster_code VARCHAR(64);
    DECLARE v_metric_type VARCHAR(32);
    DECLARE v_minute_time DATETIME;
    DECLARE v_avg_value DOUBLE;
    DECLARE v_max_value DOUBLE;
    DECLARE v_min_value DOUBLE;
    DECLARE v_data_count INT;
    DECLARE v_metric_unit VARCHAR(16);

    -- 游标：获取需要聚合的分钟数据
    DECLARE cur CURSOR FOR
        SELECT
            cluster_code,
            metric_type,
            DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00') as minute_time,
            AVG(metric_value) as avg_value,
            MAX(metric_value) as max_value,
            MIN(metric_value) as min_value,
            COUNT(*) as data_count,
            MAX(metric_unit) as metric_unit
        FROM cluster_monitor_raw_data
        WHERE report_time >= DATE_SUB(NOW(), INTERVAL 2 MINUTE)
          AND report_time < DATE_SUB(NOW(), INTERVAL 1 MINUTE)
        GROUP BY cluster_code, metric_type, DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00');

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;

    read_loop: LOOP
        FETCH cur INTO v_cluster_code, v_metric_type, v_minute_time, v_avg_value, v_max_value, v_min_value, v_data_count, v_metric_unit;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 插入或更新分钟聚合数据
        INSERT INTO cluster_monitor_minute_data
            (cluster_code, metric_type, minute_time, avg_value, max_value, min_value, data_count, metric_unit)
        VALUES
            (v_cluster_code, v_metric_type, v_minute_time, v_avg_value, v_max_value, v_min_value, v_data_count, v_metric_unit)
        ON DUPLICATE KEY UPDATE
            avg_value = v_avg_value,
            max_value = v_max_value,
            min_value = v_min_value,
            data_count = v_data_count,
            metric_unit = v_metric_unit,
            update_time = NOW();

    END LOOP;

    CLOSE cur;
END$$
DELIMITER ;
