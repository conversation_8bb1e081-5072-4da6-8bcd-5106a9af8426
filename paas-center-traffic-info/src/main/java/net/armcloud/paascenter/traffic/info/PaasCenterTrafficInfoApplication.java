package net.armcloud.paascenter.traffic.info;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"net.armcloud.paascenter.traffic.info.internal.*"})
public class PaasCenterTrafficInfoApplication {

    public static void main(String[] args) {
        SpringApplication.run(PaasCenterTrafficInfoApplication.class, args);
    }

}
