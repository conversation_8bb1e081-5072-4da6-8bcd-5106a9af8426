package net.armcloud.paascenter.traffic.info.common.buffer;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 内存批缓冲 —— 条数或时间阈值触发，一致性可配置、支持重试/死信、背压友好。
 * 典型用途：批写 ClickHouse / ES / MQ / 文件。
 *
 * <pre>
 * SafeMemBuffer<TrafficDTO> buf = SafeMemBuffer.<TrafficDTO>builder()
 *     .capacity(100_000)                       // 队列上限
 *     .batchSize(100)                          // 条数阈
 *     .flushInterval(Duration.ofSeconds(10))   // 时间阈
 *     .flushAction(list -> ck.saveBatch(list)) // 写 ClickHouse
 *     .build();
 * buf.add(dto);                                // 生产数据
 * </pre>
 */
public final class SafeMemBuffer<T> implements AutoCloseable {

    /* =========================  配置  ========================= */

    public enum BackPressure { BLOCK, DROP_OLDEST, DROP_NEWEST }

    public static final class Config<T> {
        int                 capacity            = 10_000;
        int                 batchSize           = 5_000;
        Duration            flushInterval       = Duration.ofSeconds(2);
        BackPressure        backPressure        = BackPressure.BLOCK;
        Supplier<ExecutorService> flushPool     = defaultFlushPool();
        Consumer<List<T>>   flushAction;                                // ★ 必填
        Consumer<Throwable> errorHandler        = Throwable::printStackTrace;
        Runnable            successCallback     = () -> {};

        int                 maxRetries          = 3;
        Duration            initialRetryDelay   = Duration.ofSeconds(1);
        double              retryDelayMultiplier= 2.0;
        Duration            maxRetryDelay       = Duration.ofMinutes(5);
        Consumer<List<T>>   deadLetterHandler   = list ->
                System.err.println("DEAD-LETTER[" + list.size() + "] " + list);
        /** 默认单线程池，自动回压到调度线程，避免无限排队 */
        private static Supplier<ExecutorService> defaultFlushPool() {
            return () -> new ThreadPoolExecutor(
                    1, 2,
                    60, TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(1_000),            // 有界
                    r -> { Thread t=new Thread(r,"buffer-flush");t.setDaemon(true);return t; },
                    new ThreadPoolExecutor.CallerRunsPolicy()); // 队列满回调线程执行
        }
    }

    /* =========================  Builder  ========================= */

    public static <T> Builder<T> builder() { return new Builder<>(); }

    public static final class Builder<T> {
        private final Config<T> c = new Config<>();
        public Builder<T> capacity(int n)                     { c.capacity=n; return this; }
        public Builder<T> batchSize(int n)                    { c.batchSize=n;return this;}
        public Builder<T> flushInterval(Duration d)           { c.flushInterval=d;return this;}
        public Builder<T> backPressure(BackPressure bp)       { c.backPressure=bp;return this;}
        public Builder<T> flushPool(Supplier<ExecutorService> s){ c.flushPool=s;return this;}
        public Builder<T> flushAction(Consumer<List<T>> a)    { c.flushAction=a;return this;}
        public Builder<T> onError(Consumer<Throwable> e)      { c.errorHandler=e;return this;}
        public Builder<T> onSuccess(Runnable cb)              { c.successCallback=cb;return this;}
        public Builder<T> maxRetries(int n)                   { c.maxRetries=n;return this;}
        public Builder<T> initialRetryDelay(Duration d)       { c.initialRetryDelay=d;return this;}
        public Builder<T> retryDelayMultiplier(double m)      { c.retryDelayMultiplier=m;return this;}
        public Builder<T> maxRetryDelay(Duration d)           { c.maxRetryDelay=d;return this;}
        public Builder<T> deadLetterHandler(Consumer<List<T>> h){ c.deadLetterHandler=h;return this;}

        public SafeMemBuffer<T> build() { return new SafeMemBuffer<>(c); }
    }

    /* =========================  实现  ========================= */

    private final ArrayBlockingQueue<T>  queue;
    private final Config<T>              cfg;
    private final ScheduledExecutorService scheduler;
    private final ExecutorService        flushPool;
    private final ConcurrentMap<UUID, RetryBatch<T>> retries = new ConcurrentHashMap<>();
    private final AtomicInteger          activeFlush = new AtomicInteger(0);
    private volatile boolean             closed = false;

    private SafeMemBuffer(Config<T> cfg) {
        Objects.requireNonNull(cfg.flushAction, "flushAction required");
        this.cfg = cfg;
        this.queue = new ArrayBlockingQueue<>(cfg.capacity);
        this.flushPool = cfg.flushPool.get();

        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t=new Thread(r,"buffer-scheduler"); t.setDaemon(true); return t;
        });
        scheduler.scheduleAtFixedRate(this::process, cfg.flushInterval.toMillis(),
                cfg.flushInterval.toMillis(), TimeUnit.MILLISECONDS);
        Runtime.getRuntime().addShutdownHook(new Thread(this::close));
    }

    /* ------------ 生产 ------------ */

    public boolean add(T t) {
        if (closed) throw new IllegalStateException("buffer closed");

        boolean ok;
        switch (cfg.backPressure) {
            case DROP_NEWEST:  ok = queue.offer(t); break;
            case DROP_OLDEST:
                while (!(ok=queue.offer(t))) queue.poll();
                break;
            default:
                try { queue.put(t); ok=true; }
                catch (InterruptedException e){ Thread.currentThread().interrupt(); return false; }
        }

        // 条数阈值即时触发
        if (queue.size() >= cfg.batchSize) flushAsync();
        return ok;
    }

    /* ------------ 调度 ------------ */

    private void process() {
        if (closed && queue.isEmpty() && retries.isEmpty() && activeFlush.get()==0) {
            scheduler.shutdown();
            return;
        }

        // 1. 拉主队列
        List<T> batch = new ArrayList<>(cfg.batchSize);
        queue.drainTo(batch, cfg.batchSize);
        if (!batch.isEmpty()) submit(batch, 0, null);

        // 2. 检查重试队列
        long now = System.currentTimeMillis();
        retries.forEach((id, rb) -> {
            if (rb.nextAttempt<=now && retries.remove(id)!=null) {
                submit(new ArrayList<>(rb.batch), rb.retry+1, id);
            }
        });
    }

    private void flushAsync() { flushPool.execute(this::flushOnce); } // fallback

    /* ------------ 提交 Flush ------------ */

    private void submit(List<T> batch, int retry, UUID id) {
        if (batch.isEmpty()) return;
        activeFlush.incrementAndGet();

        flushPool.execute(() -> {
            try {
                cfg.flushAction.accept(batch);
                cfg.successCallback.run();
            } catch (Throwable ex) {
                cfg.errorHandler.accept(ex);
                handleFail(batch, retry, id);
            } finally {
                activeFlush.decrementAndGet();
            }
        });
    }

    private void handleFail(List<T> batch, int retry, UUID id) {
        if (retry >= cfg.maxRetries) {
            try { cfg.deadLetterHandler.accept(batch); }
            catch (Throwable dlh) { cfg.errorHandler.accept(dlh); }
            return;
        }
        long delay = (long)(cfg.initialRetryDelay.toMillis()
                * Math.pow(cfg.retryDelayMultiplier, retry));
        delay = Math.min(delay, cfg.maxRetryDelay.toMillis());
        UUID newId = id!=null? id : UUID.randomUUID();
        retries.put(newId, new RetryBatch<>(batch, retry, System.currentTimeMillis()+delay));
    }

    private void flushOnce() {/* no-op placeholder */}

    /* ------------ 关闭 ------------ */

    @Override public void close() {
        if (closed) return;
        closed = true;

        scheduler.shutdown();
        try { scheduler.awaitTermination(10, TimeUnit.SECONDS); }
        catch (InterruptedException e){ Thread.currentThread().interrupt(); }

        // 最终 flush 主队列
        List<T> rest = new ArrayList<>();
        queue.drainTo(rest);
        if (!rest.isEmpty()) safeFinalFlush(rest);

        // flush 重试批次
        retries.values().forEach(rb -> safeFinalFlush(new ArrayList<>(rb.batch)));
        retries.clear();

        flushPool.shutdown();
        try { flushPool.awaitTermination(60, TimeUnit.SECONDS); }
        catch (InterruptedException e){ Thread.currentThread().interrupt(); }
    }

    private void safeFinalFlush(List<T> batch) {
        try { cfg.flushAction.accept(batch); }
        catch (Throwable ex){ cfg.errorHandler.accept(ex); }
    }

    /* ------------ 内部类 ------------ */

    private static final class RetryBatch<T>{
        final List<T> batch;
        final int     retry;
        final long    nextAttempt;
        RetryBatch(List<T> b,int r,long t){this.batch=b;this.retry=r;this.nextAttempt=t;}
    }
}