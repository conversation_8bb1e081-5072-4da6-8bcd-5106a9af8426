//package net.armcloud.paascenter.traffic.info.config;
//
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.jdbc.DataSourceBuilder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//
//import javax.sql.DataSource;
//
///**
// * <AUTHOR>
// * @Date 2025/4/28 20:37
// * @Description:
// */
//@Configuration
//@MapperScan("net.armcloud.paascenter.traffic.info.mapper.**")
//public class DataSourceConfig {
//
//    @Primary
//    @Bean(name = "masterDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.master")
//    public DataSource masterDataSource() {
//        return DataSourceBuilder.create().build();
//    }
//
//    @Bean(name = "paasCenterCore")
//    @ConfigurationProperties(prefix = "spring.datasource.paas-center-core")
//    public DataSource paasCenterCoreDataSource() {
//        return DataSourceBuilder.create().build();
//    }
//
//    @Bean(name = "paasCenterCoreSqlSessionFactory")
//    public SqlSessionFactory paasCenterCoreSqlSessionFactory(
//            @Qualifier("paasCenterCore") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean sessionFactoryBean = new SqlSessionFactoryBean();
//        sessionFactoryBean.setDataSource(dataSource);
//        sessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
//        return sessionFactoryBean.getObject();
//    }
//}
//
