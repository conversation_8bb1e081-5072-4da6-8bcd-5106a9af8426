package net.armcloud.paascenter.traffic.info.config.datasource;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * ClickHouse数据源配置
 */
@Slf4j
@Configuration
@MapperScan(basePackages = "net.armcloud.paascenter.traffic.info.mapper.clickhouse", sqlSessionTemplateRef = "ckSqlSessionTemplate", annotationClass = Mapper.class)
public class ClickHouseDatasourceConfig {

    /**
     * ClickHouse数据源
     */
    @Bean(name = "ckDataSource")
    @ConfigurationProperties(prefix = "spring.clickhouse")
    public DataSource ckDataSourceConfig() {
        log.info("初始化ClickHouse数据源...");
        HikariDataSource dataSource = new HikariDataSource();
        
        // 确保驱动类已加载
        try {
            Class.forName("com.clickhouse.jdbc.ClickHouseDriver");
            log.info("成功加载 ClickHouse 驱动类");
        } catch (ClassNotFoundException e) {
            log.error("加载 ClickHouse 驱动类失败", e);
            throw new RuntimeException("ClickHouse驱动类加载失败", e);
        }
        
        log.info("ClickHouse DataSource bean created successfully");
        return dataSource;
    }

    /**
     * ClickHouse会话工厂
     */
    @Bean(name = "ckSqlSessionFactory")
    public SqlSessionFactory ckSqlSessionFactory(@Qualifier("ckDataSource") DataSource dataSource) throws Exception {
        log.info("创建ClickHouse SQLSessionFactory, 数据源类型: {}", dataSource.getClass().getName());
        
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/clickhouse/*.xml"));
        
        // ClickHouse特定配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);  // 驼峰命名转换
        configuration.setCacheEnabled(false);            // ClickHouse不需要缓存
        configuration.setCallSettersOnNulls(true);       // 空值也调用setter
        configuration.setUseGeneratedKeys(false);        // ClickHouse不支持自增主键
        bean.setConfiguration(configuration);
        
        return bean.getObject();
    }

    /**
     * ClickHouse事务管理器
     * 注：ClickHouse实际不支持事务，此处为MyBatis框架需要
     */
    @Bean(name = "ckTransactionManager")
    public DataSourceTransactionManager ckTransactionManager(
            @Qualifier("ckDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * ClickHouse会话模板
     */
    @Bean(name = "ckSqlSessionTemplate")
    public SqlSessionTemplate ckSqlSessionTemplate(
            @Qualifier("ckSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
} 