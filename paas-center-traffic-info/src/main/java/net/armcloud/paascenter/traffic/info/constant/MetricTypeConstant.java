package net.armcloud.paascenter.traffic.info.constant;

/**
 * 监控指标类型常量
 * 
 * <AUTHOR>
 */
public class MetricTypeConstant {

    /**
     * Ceph压力指标
     */
    public static final String CEPH_PRESSURE = "ceph_pressure";

    /**
     * 写入速率指标
     */
    public static final String WRITE_RATE = "write_rate";

    /**
     * 容量占用指标
     */
    public static final String CAPACITY_USAGE = "capacity_usage";

    /**
     * 读取速率指标
     */
    public static final String READ_RATE = "read_rate";

    /**
     * IOPS指标
     */
    public static final String IOPS = "iops";

    /**
     * 延迟指标
     */
    public static final String LATENCY = "latency";
}
