package net.armcloud.paascenter.traffic.info.controller;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.domain.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/traffic-info/open/heartbeat")
public class ServiceHeartbeatController {
    /**
     * 服务心跳检测
     */
    @GetMapping("status")
    public Result<?> stsToken() {
        return Result.ok();
    }
}
