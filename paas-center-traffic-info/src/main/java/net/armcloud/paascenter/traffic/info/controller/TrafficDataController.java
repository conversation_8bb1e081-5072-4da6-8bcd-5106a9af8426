package net.armcloud.paascenter.traffic.info.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.dto.CephPressureReportDTO;
import net.armcloud.paascenter.traffic.info.model.dto.DiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.dto.TrafficDataDTO;
import net.armcloud.paascenter.traffic.info.service.IPadTrafficInfoService;
import net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@Slf4j
@RestController
@RequestMapping("/traffic-info/open/traffic")
public class TrafficDataController {
    @Autowired
    @Qualifier("padTrafficInfoServiceImpl")
    private IPadTrafficInfoService padTrafficInfoService;

    @Resource
    private TaskExecutor trafficDiskExecutor;

    @Resource
    private TaskExecutor trafficScreenshotExecutor;

    @Resource
    private PadTrafficInfoClickHouseService trafficInfoClickHouseService;

    @PostMapping("/save")
    @ApiOperation(value = "实例流量数据信息")
    public Result<?> screenshotLocal(@Valid @RequestBody TrafficDataDTO param) {
        trafficScreenshotExecutor.execute(() -> {
            try {
                padTrafficInfoService.sendMessage(param);
            } catch (Exception e) {
                log.error("实例流量数据信息", e);
            }
        });
        return Result.ok();
    }


    /**
     * cbs上报磁盘信息
     */
    @PostMapping("/disk/info")
    public Result<?> diskInfo(@Valid @RequestBody DiskInfoDTO dto) {
        log.info("cbs上报磁盘信息:{}", JSONObject.toJSONString(dto));
        //集合超过设置大小直接丢弃
        if ((CollectionUtil.isNotEmpty(dto.getTop10CpuInfo()) && dto.getTop10CpuInfo().size() > 10)
                || (CollectionUtil.isNotEmpty(dto.getTop10IOInfo()) && dto.getTop10IOInfo().size() > 30)) {
            log.info("cbs上报磁盘信息_丢弃:{}", JSONObject.toJSONString(dto));
            return Result.ok();
        }
        trafficDiskExecutor.execute(() -> {
            try {
                padTrafficInfoService.reportDiskInfo(dto);
            } catch (Exception e) {
                log.error("cbs上报磁盘信息失败", e);
            }
        });
        return Result.ok();
    }

    @PostMapping("/cbs/resource/report")
    public Result<?> resourceReport(@Valid @RequestBody JSONObject json) {
        log.info("cbs资源使用信息:{}", json);
        return Result.ok();

    }

    @PostMapping("/edge/ceph/report")
    public Result<Void> cephReport(@Valid @RequestBody JSONObject json) {
        log.info("ceph:{}", json);
        CephPressureReportDTO requestDTO = json.toJavaObject(CephPressureReportDTO.class);

        return Result.ok();
    }

}
