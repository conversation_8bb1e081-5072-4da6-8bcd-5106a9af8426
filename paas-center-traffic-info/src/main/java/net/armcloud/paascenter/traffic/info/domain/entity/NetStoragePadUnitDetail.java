package net.armcloud.paascenter.traffic.info.domain.entity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/4/16 11:48
 * @Description:
 */


@Data
@EqualsAndHashCode(callSuper = false)
@TableName("net_storage_pad_unit_detail")  // 表明该类对应数据库中的表
public class NetStoragePadUnitDetail {

    @TableId
    private Long netStoragePadUnitDetailId;  // 网存创建信息主键id

    @TableField("cluster_code")
    private String clusterCode;  // 集群code

    @TableField("real_phone_template_id")
    private Long realPhoneTemplateId;  // 真机模板Id

    @TableField("net_storage_res_unit_code")
    private String netStorageResUnitCode;  // 网络存储code

    @TableField("pad_code")
    private String padCode;  // 实例code

    @TableField("device_android_prop")
    private String deviceAndroidProp;  // 创建实例的安卓属性

    @TableField("net_storage_res_apply_size")
    private String netStorageResApplySize;  // 申请的存储大小

    @TableField("net_storage_res_use_size")
    private String netStorageResUseSize;  // 网存实际使用的大小
}

