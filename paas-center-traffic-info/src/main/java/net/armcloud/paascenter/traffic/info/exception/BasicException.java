package net.armcloud.paascenter.traffic.info.exception;

import lombok.Getter;
import lombok.Setter;

import static net.armcloud.paascenter.traffic.info.exception.BasicExceptionCode.PROCESSING_FAILED;


@Setter
@Getter
public class BasicException extends RuntimeException {
    private final ExceptionCode exceptionCode;

    public BasicException(String errorMsg) {
        super(errorMsg);
        exceptionCode = new ExceptionCode() {
            @Override
            public int getStatus() {
                return PROCESSING_FAILED.getStatus();
            }

            @Override
            public String getMsg() {
                return errorMsg;
            }
        };
    }

    public BasicException(String errorMsg, int status) {
        super(errorMsg);
        exceptionCode = new ExceptionCode() {
            @Override
            public int getStatus() {
                return status;
            }

            @Override
            public String getMsg() {
                return errorMsg;
            }
        };
    }

    public BasicException() {
        super(PROCESSING_FAILED.getMsg());
        this.exceptionCode = PROCESSING_FAILED;
    }

    public BasicException(ExceptionCode exceptionCode) {
        super(exceptionCode.getMsg());
        this.exceptionCode = exceptionCode;
    }

    public BasicException(ExceptionCode exceptionCode, String str) {
        super(str);
        this.exceptionCode = exceptionCode;
    }

    public BasicException(int status, String msg) {
        super(msg);
        exceptionCode = new ExceptionCode() {
            @Override
            public int getStatus() {
                return status;
            }

            @Override
            public String getMsg() {
                return msg;
            }
        };
    }
}
