package net.armcloud.paascenter.traffic.info.internal.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class CephPressureReportDTO implements Serializable {

    private final static long serialVersionUID = 1L;
    /** {"deviceType":"CBS","clusterCode":"001", "metrics":{"cephPressure":"32.12"}} **/
//    // 设备类型
//    private String deviceType;
    // 集群编号
    private String clusterCode;
    // 集群指标
    private Metrics metrics;

    @Data
    public static class Metrics {
        // 压力百分比
        private Double cephPressure;
    }
}
