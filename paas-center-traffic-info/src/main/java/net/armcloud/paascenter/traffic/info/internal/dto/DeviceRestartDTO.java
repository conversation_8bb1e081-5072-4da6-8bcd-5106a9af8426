package net.armcloud.paascenter.traffic.info.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeviceRestartDTO {
    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "板卡数量不少于于1个")
    private List<String> deviceIps;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;
}
