package net.armcloud.paascenter.traffic.info.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class RestartDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    private List<String> padCodes;

    @ApiModelProperty(value = "实例组ID")
    private List<Integer> groupIds;

    @ApiModelProperty(value = "是否更换IP(默认不更换)")
    private Boolean changeIpFlag =false;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    /**
     * userId
     */
    @ApiModelProperty(hidden = true)
    private Long userId;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}
