package net.armcloud.paascenter.traffic.info.internal.dto;

import lombok.Data;

import java.util.List;

@Data
public class SendPadStatusDTO {
    /**
     * 云机编号
     */
    private List<String> padCodes;
    /**
     * 云机状态
     */
    private Integer padStatus;
    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 操作业务
     */
    private String oprBusiness;

    public SendPadStatusDTO(){

    }
    public SendPadStatusDTO(List<String> padCodes, Integer padStatus, Long customerId, String oprBusiness) {
        this.padCodes = padCodes;
        this.padStatus = padStatus;
        this.customerId = customerId;
        this.oprBusiness = oprBusiness;
    }
}
