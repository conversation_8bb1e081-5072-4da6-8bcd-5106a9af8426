package net.armcloud.paascenter.traffic.info.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SetDeviceGatewayDTO {

    @ApiModelProperty(value = "板卡编号列表")
    @NotNull(message = "板卡编号不能为空")
    @Size(min = 1, max = 100, message = "设置板卡数量请勿超过100")
    private List<String> deviceCodes;

    /**
     * 网关
     */
    private String gateway;
    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    /**
     * 开放平台访问用户ID
     */
    private Long userId;

    /**
     * 任务来源
     */
    private String taskSource = SourceTargetEnum.PAAS.getCode();

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}
