package net.armcloud.paascenter.traffic.info.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class VirtualizeDeviceDTO {

    @ApiModelProperty(value = "物理机IP列表")
    @NotNull(message = "deviceIps cannot null")
    @Size(min = 1, max = 128, message = "设置板卡数量请勿超过128")
    private List<String> deviceIps;

    @ApiModelProperty(value = "实例规则编码")
    @NotNull(message = "specificationCode cannot null")
    private String specificationCode;

    @NotNull(message = "imageId cannot null")
    @ApiModelProperty(value = "实例镜像ID")
    private String imageId;

    @NotNull(message = "screenLayoutCode cannot null")
    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    @ApiModelProperty(value = "是否隔离CPU")
    private Boolean isolateCpu = true;

    @ApiModelProperty(value = "是否隔离内存")
    private Boolean isolateMemory = true;

    @ApiModelProperty(value = "是否限制存储")
    private Boolean isolateStorage = true;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "操作人")
    private String oprBy;

    @ApiModelProperty(value = "任务来源")
    private String sourceCode;

    /**
     * 推流类型
     */
    private Integer streamType;

    /**
     * 实例dns
     */
    private String dns;
    /**
     * 实例 adi
     */
    private String adiUrl;
    /**
     * adi密码
     */
    private String adiPassword;
    /**
     * 实例安卓系统属性
     */
    private String deviceAndroidProps;

    /**
     * 实例类型
     *
     */
    private String padType;
}
