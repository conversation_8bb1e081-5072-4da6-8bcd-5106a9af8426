package net.armcloud.paascenter.traffic.info.internal.facade;

import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.traffic.info.domain.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface DCInternalFacade {

    /**
     * 查询机房信息
     */
    @GetMapping(value = "/openapi/internal/dc/getByPadCode")
    Result<DcInfo> getByPadCode(@RequestParam("padCode") String padCode);

}
