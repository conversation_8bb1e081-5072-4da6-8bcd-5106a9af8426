package net.armcloud.paascenter.traffic.info.internal.facade;

import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.dto.*;
import net.armcloud.paascenter.traffic.info.internal.vo.DeviceVO;
import net.armcloud.paascenter.traffic.info.internal.vo.GenerateDeviceTaskVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

public interface DeviceInternalFacade {
    /**
     * 根据deviceIp查询物理机
     *
     * @param deviceIp
     * @return
     */
    @GetMapping("/openapi/internal/pad/selectByDeviceIp")
    public Result<Device> selectByDeviceIp(@RequestParam("deviceIp") String deviceIp);


    /**
     * 根据deviceIp查询客户信息
     * @param deviceIp
     * @return
     */
    @GetMapping("/openapi/internal/pad/selectCustomerByDeviceIp")
    public Result<DeviceVO> selectCustomerByDeviceIp(@RequestParam("deviceIp") String deviceIp);







}
