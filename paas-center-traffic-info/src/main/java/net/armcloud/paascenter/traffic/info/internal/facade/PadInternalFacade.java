package net.armcloud.paascenter.traffic.info.internal.facade;

import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.vo.api.AsyncCmdVO;
import net.armcloud.paascenter.common.model.vo.api.PadAdbVO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import net.armcloud.paascenter.common.model.vo.api.SyncCmdVO;
import net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.dto.*;
import net.armcloud.paascenter.traffic.info.internal.vo.GeneratePadBackupTaskVO;
import net.armcloud.paascenter.traffic.info.internal.vo.GeneratePadTaskInfoVO;
import net.armcloud.paascenter.traffic.info.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.traffic.info.internal.vo.PadInfoVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;


public interface PadInternalFacade {

    /**
     * 根据ip查询实例信息
     *
     * @param param
     * @return
     */
    @PostMapping("/openapi/internal/pad/getPadByOutCodeAndIp")
    Result<Pad> getPadByOutCodeAndIp(@RequestBody PadIpDTO param);

}
