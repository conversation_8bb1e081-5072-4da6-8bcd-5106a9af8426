package net.armcloud.paascenter.traffic.info.internal.vo;

import lombok.Data;

@Data
public class AddDeviceTaskVO {
    private String deviceCode;

    private Long masterTaskId;
    private String masterUniqueId;

    private Long subTaskId;
    private String subTaskUniqueId;
    private Integer subTaskStatus;
    private String errorMsg;
    private Long containerTaskId;
    /**
     *  pad_task-->customer_task_id
     */
    private Integer customerTaskId;
    private String deviceIp;
    private String deviceDns;
    private String deviceNetmask;
    private String deviceOutCode;
    private String armServerCode;

    private Long customerId;
    private String armIp;

    private String clusterCode;

}
