package net.armcloud.paascenter.traffic.info.mapper.clickhouse;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.traffic.info.model.entity.ArmcloudDiskInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

/**
 * 云手机磁盘信息Mapper接口
 */
@Mapper
@Repository
public interface DiskInfoMapper extends BaseMapper<ArmcloudDiskInfo> {

    /**
     * 批量插入磁盘信息数据
     *
     * @param records
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<Map<String, Object>> records);

}