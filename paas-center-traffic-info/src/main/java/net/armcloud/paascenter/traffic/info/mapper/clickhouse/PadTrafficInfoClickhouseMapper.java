package net.armcloud.paascenter.traffic.info.mapper.clickhouse;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.traffic.info.model.entity.PadTrafficInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface PadTrafficInfoClickhouseMapper extends BaseMapper<PadTrafficInfo> {

    int batchInsert(@Param("list") List<PadTrafficInfo> records);

    int batchInsert2(@Param("list") List<Map<String, Object>> records);

}