package net.armcloud.paascenter.traffic.info.mapper.paas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.traffic.info.model.entity.CephPressureData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Ceph压力数据Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface CephPressureDataMapper extends BaseMapper<CephPressureData> {

    /**
     * 批量插入Ceph压力数据
     * 
     * @param dataList 数据列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<CephPressureData> dataList);

    /**
     * 根据时间区间查询分钟维度的数据
     * 
     * @param clusterCode 集群编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分钟维度数据列表
     */
    List<Map<String, Object>> selectMinuteData(@Param("clusterCode") String clusterCode,
                                               @Param("startTime") Date startTime,
                                               @Param("endTime") Date endTime);

    /**
     * 根据时间区间查询聚合数据（当数据量超过1000时使用）
     * 
     * @param clusterCode 集群编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param intervalMinutes 聚合间隔（分钟）
     * @return 聚合数据列表
     */
    List<Map<String, Object>> selectAggregatedData(@Param("clusterCode") String clusterCode,
                                                   @Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime,
                                                   @Param("intervalMinutes") int intervalMinutes);

    /**
     * 统计指定时间区间内的数据条数
     * 
     * @param clusterCode 集群编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据条数
     */
    int countByTimeRange(@Param("clusterCode") String clusterCode,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);

    /**
     * 删除指定时间之前的数据（用于数据清理）
     * 
     * @param beforeTime 时间点
     * @return 删除的行数
     */
    int deleteBeforeTime(@Param("beforeTime") Date beforeTime);
}
