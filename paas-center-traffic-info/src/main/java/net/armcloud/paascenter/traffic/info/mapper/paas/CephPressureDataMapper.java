package net.armcloud.paascenter.traffic.info.mapper.paas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorRawData;
import net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorMinuteData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 集群监控数据Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ClusterMonitorDataMapper extends BaseMapper<ClusterMonitorRawData> {

    /**
     * 批量插入原始监控数据
     *
     * @param dataList 数据列表
     * @return 影响行数
     */
    int batchInsertRawData(@Param("list") List<ClusterMonitorRawData> dataList);

    /**
     * 从原始表查询分钟维度的数据（时间跨度<=30分钟）
     *
     * @param clusterCode 集群编码
     * @param metricType 指标类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分钟维度数据列表
     */
    List<Map<String, Object>> selectMinuteDataFromRaw(@Param("clusterCode") String clusterCode,
                                                      @Param("metricType") String metricType,
                                                      @Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime);

    /**
     * 从分钟聚合表查询数据（时间跨度>30分钟）
     *
     * @param clusterCode 集群编码
     * @param metricType 指标类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分钟维度数据列表
     */
    List<Map<String, Object>> selectMinuteDataFromAgg(@Param("clusterCode") String clusterCode,
                                                      @Param("metricType") String metricType,
                                                      @Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime);

    /**
     * 从分钟聚合表查询聚合数据（当数据量超过1000时使用）
     *
     * @param clusterCode 集群编码
     * @param metricType 指标类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param intervalMinutes 聚合间隔（分钟）
     * @return 聚合数据列表
     */
    List<Map<String, Object>> selectAggregatedDataFromAgg(@Param("clusterCode") String clusterCode,
                                                          @Param("metricType") String metricType,
                                                          @Param("startTime") Date startTime,
                                                          @Param("endTime") Date endTime,
                                                          @Param("intervalMinutes") int intervalMinutes);

    /**
     * 统计原始表中指定时间区间内的数据条数
     *
     * @param clusterCode 集群编码
     * @param metricType 指标类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据条数
     */
    int countRawDataByTimeRange(@Param("clusterCode") String clusterCode,
                                @Param("metricType") String metricType,
                                @Param("startTime") Date startTime,
                                @Param("endTime") Date endTime);

    /**
     * 统计分钟聚合表中指定时间区间内的数据条数
     *
     * @param clusterCode 集群编码
     * @param metricType 指标类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据条数
     */
    int countMinuteDataByTimeRange(@Param("clusterCode") String clusterCode,
                                   @Param("metricType") String metricType,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime);

    /**
     * 删除原始表中指定时间之前的数据（保留1个月）
     *
     * @param beforeTime 时间点
     * @return 删除的行数
     */
    int deleteRawDataBeforeTime(@Param("beforeTime") Date beforeTime);

    /**
     * 删除分钟聚合表中指定时间之前的数据（保留3个月）
     *
     * @param beforeTime 时间点
     * @return 删除的行数
     */
    int deleteMinuteDataBeforeTime(@Param("beforeTime") Date beforeTime);

    /**
     * 插入或更新分钟聚合数据
     *
     * @param data 分钟聚合数据
     * @return 影响行数
     */
    int insertOrUpdateMinuteData(@Param("data") ClusterMonitorMinuteData data);

    /**
     * 批量插入分钟聚合数据
     *
     * @param dataList 数据列表
     * @return 影响行数
     */
    int batchInsertMinuteData(@Param("list") List<ClusterMonitorMinuteData> dataList);
}
