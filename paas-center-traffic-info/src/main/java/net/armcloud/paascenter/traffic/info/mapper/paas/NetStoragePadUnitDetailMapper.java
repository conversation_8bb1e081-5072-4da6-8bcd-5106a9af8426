package net.armcloud.paascenter.traffic.info.mapper.paas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.traffic.info.domain.entity.NetStoragePadUnitDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2025/4/16 11:32
 * @Description:
 */
@Mapper
public interface NetStoragePadUnitDetailMapper extends BaseMapper<NetStoragePadUnitDetail> {
    void updateByPadCode(NetStoragePadUnitDetail item);
    String selectResUnitCodeByPadCode(@Param("padCode") String padCode);
    void updateNetStorageResUnitUsedSizeByPadCode(NetStoragePadUnitDetail item);


}
