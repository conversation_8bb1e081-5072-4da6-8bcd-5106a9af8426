package net.armcloud.paascenter.traffic.info.mapper.traffic;

import net.armcloud.paascenter.common.model.entity.traffic.DeviceSystemConfigData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface DeviceSystemConfigDataMapper {

    int insertDeviceSystemConfig(@Param("param") DeviceSystemConfigData param);

    @Select("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'device_system_config_data_${date}'")
    int checkTableExists(@Param("date") String date);

    void createTable(@Param("date") String date);

    @Delete("DROP TABLE IF EXISTS device_system_config_data_${date}")
    void dropTable(String threeDaysAgo);
}
