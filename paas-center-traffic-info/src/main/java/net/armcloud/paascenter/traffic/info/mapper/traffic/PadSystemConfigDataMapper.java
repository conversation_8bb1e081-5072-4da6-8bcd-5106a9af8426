package net.armcloud.paascenter.traffic.info.mapper.traffic;

import net.armcloud.paascenter.common.model.entity.traffic.PadSystemConfigData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PadSystemConfigDataMapper {
    int insertPadSystemConfig(@Param("param") PadSystemConfigData param);

    @Select("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'pad_system_config_data_${date}'")
    int checkTableExists(String date);

    @Delete("DROP TABLE IF EXISTS pad_system_config_data_${date}")
    void dropTable(String threeDaysAgo);

    void createTable(@Param("date") String date);
}
