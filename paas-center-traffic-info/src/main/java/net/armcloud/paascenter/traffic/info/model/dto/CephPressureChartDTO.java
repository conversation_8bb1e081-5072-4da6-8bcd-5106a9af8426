package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Ceph压力折线图数据DTO
 * 
 * <AUTHOR>
 */
@Data
public class CephPressureChartDTO {

    /**
     * 时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timePoint;

    /**
     * 平均压力值
     */
    private Double avgPressure;

    /**
     * 最大压力值
     */
    private Double maxPressure;

    /**
     * 最小压力值
     */
    private Double minPressure;

    /**
     * 数据点数量
     */
    private Integer dataCount;

    /**
     * 时间点字符串（用于前端显示）
     */
    private String timeStr;

    public CephPressureChartDTO() {}

    public CephPressureChartDTO(Date timePoint, Double avgPressure, Double maxPressure, 
                               Double minPressure, Integer dataCount) {
        this.timePoint = timePoint;
        this.avgPressure = avgPressure;
        this.maxPressure = maxPressure;
        this.minPressure = minPressure;
        this.dataCount = dataCount;
    }
}
