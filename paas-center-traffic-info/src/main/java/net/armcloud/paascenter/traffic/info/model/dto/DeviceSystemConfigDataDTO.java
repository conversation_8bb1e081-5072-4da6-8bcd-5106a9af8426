package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class DeviceSystemConfigDataDTO {

    /**
     * 物理机IP
     */
    @NotBlank(message = "deviceIp cannot null")
    private String deviceIp;

    /**
     * 打点数据
     */
    @NotBlank(message = "systemData cannot null")
    private UsageDataDTO systemData;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotBlank(message = "billingTime cannot null")
    private Date billingTime;
}
