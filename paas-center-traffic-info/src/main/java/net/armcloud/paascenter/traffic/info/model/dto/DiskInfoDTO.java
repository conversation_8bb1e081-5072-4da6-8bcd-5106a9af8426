package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DiskInfoDTO {
    /**
     * 机房ID
     */
    private Integer dcId;
    /**
     * 板卡IP
     */
    private String  deviceIp;

    /**
     * 256字节信息
     */
    private String  dataInfo;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateTime;

    /**
     * cpuTOP前10信息
     */
    private List<Top10CpuInfoDTO> top10CpuInfo;

    /**
     * ioTOP前10信息
     */
    private List<Top10IOInfoDTO> top10IOInfo;

    /**
     * 容量使用上报
     */
    private List<DiskUsageDetail> diskUsageDetailsList;
}
