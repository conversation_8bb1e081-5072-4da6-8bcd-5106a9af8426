package net.armcloud.paascenter.traffic.info.model.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.armcloud.paascenter.common.model.comms.LocalConst;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class InsertDiskInfoDTO {
    /**
     * 机房ID
     */
    private Integer dcId;
    /**
     * 板卡IP
     */
    private String  deviceIp;

    /**
     * 256字节信息
     */
    private String  dataInfo;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * cpuTOP前10信息(转JSON字符串来存储)
     */
    private String strTop10CpuInfo;

    /**
     * IoTOP前10信息(转JSON字符串来存储)
     */
    private String strTop10IOInfo;


    /**
     * HOST端写入数据
     */
    private Long chwdz;

    /**
     * TLC使用寿命
     */
    private Long tlc;

    /**
     * SLC使用寿命
     */
    private Long slc;

    private String customerName;

    private Long customerId;

    private String deviceCreateTime;

    public InsertDiskInfoDTO() {}

    public InsertDiskInfoDTO(Integer dcId) {
        this.dcId = dcId;
    }

    public String getTableName() {
        String today = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        return LocalConst.DB_DEVICE_DISK_INFO + LocalConst.DB_TABLE_SPLIT
                + this.dcId + LocalConst.DB_TABLE_SPLIT + today;
    }
}
