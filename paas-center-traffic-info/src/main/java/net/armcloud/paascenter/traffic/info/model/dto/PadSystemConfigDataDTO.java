package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class PadSystemConfigDataDTO {
    /**
     * 实例外部IP
     */
    @NotBlank(message = "padOutCode cannot null")
    private String padOutCode;
    /**
     * 实例Ip
     */
    @NotBlank(message = "padIp cannot null")
    private String padIp;

    /**
     * 打点数据
     */
    @NotBlank(message = "systemData cannot null")
    private UsageDataDTO systemData;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 计费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billingTime;
}
