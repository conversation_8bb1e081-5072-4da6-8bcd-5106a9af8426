package net.armcloud.paascenter.traffic.info.model.dto;

import lombok.Data;

/**
 * 磁盘上报clickHouse
 */
@Data
public class ReportDiskInfoRequest {
    private String appId = "armcloud";
    private String page = "paas_gateway" ;
    private Integer type = 4;
    private InsertDiskInfoReportDTO properties;

    public static ReportDiskInfoRequest build(InsertDiskInfoDTO insertDiskInfoDTO){
        ReportDiskInfoRequest infoRequest = new ReportDiskInfoRequest();
        InsertDiskInfoReportDTO dto = new InsertDiskInfoReportDTO();
        dto.setDataInfo(insertDiskInfoDTO.getDataInfo());
        dto.setChwdz(insertDiskInfoDTO.getChwdz());
        dto.setCreateTime(insertDiskInfoDTO.getCreateTime());
        dto.setDateTime(insertDiskInfoDTO.getDateTime());
        dto.setDeviceIp(insertDiskInfoDTO.getDeviceIp());
        dto.setDcId(insertDiskInfoDTO.getDcId());
        /**
         * 修复Tlc字段未赋值
         */
        dto.setTlc(insertDiskInfoDTO.getTlc());
        dto.setSlc(insertDiskInfoDTO.getSlc());
        dto.setStrTop10IOInfo(insertDiskInfoDTO.getStrTop10IOInfo());
        dto.setStrTop10CpuInfo(insertDiskInfoDTO.getStrTop10CpuInfo());
        dto.setCustomerId(insertDiskInfoDTO.getCustomerId());
        dto.setCustomerName(insertDiskInfoDTO.getCustomerName());
        infoRequest.setProperties(dto);
        return infoRequest;
    }
}
