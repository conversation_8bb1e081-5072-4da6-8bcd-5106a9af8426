package net.armcloud.paascenter.traffic.info.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class TrafficDataDTO {
    /**
     * 实例外部IP
     */
    @NotBlank(message = "padOutCode cannot null")
    private String padOutCode;
    /**
     * 实例Ip
     */
    @NotBlank(message = "padIp cannot null")
    private String padIp;
    /**
     * 公网出流量/mbps
     */
    @NotNull(message = "publicOut cannot null")
    private Long publicOut;
    /**
     * 公网入流量/mbps
     */
    @NotNull(message = "publicIn cannot null")
    private Long publicIn;
    /**
     * 私有网出流量/mbps
     */
    @NotNull(message = "privateOut cannot null")
    private Long privateOut;
    /**
     * 私有网入流量/mbps
     */
    @NotNull(message = "privateIn cannot null")
    private Long privateIn;
    /**
     * 总出流量/mbps
     */
    @NotNull(message = "totalOut cannot null")
    private Long totalOut;
    /**
     * 总入流量/mbps
     */
    @NotNull(message = "totalIn cannot null")
    private Long totalIn;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 流量计费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billingTime;

}
