package net.armcloud.paascenter.traffic.info.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ClickHouse armcloud_disk_info表实体类
 */
@Data
@Accessors(chain = true)
@TableName("armcloud_disk_info")
public class ArmcloudDiskInfo {

    /**
     * 实例ID
     */
    private String instanceId;

    /**
     * 磁盘总容量(GB)
     */
    private Double totalDisk;

    /**
     * 已使用磁盘容量(GB)
     */
    private Double usedDisk;

    /**
     * 可用磁盘容量(GB)
     */
    private Double availableDisk;

    /**
     * 磁盘使用率(%)
     */
    private Double diskUsage;

    /**
     * 机房ID
     */
    private String roomId;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 服务器ID
     */
    private String serverId;

    /**
     * 采集时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectTime;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}