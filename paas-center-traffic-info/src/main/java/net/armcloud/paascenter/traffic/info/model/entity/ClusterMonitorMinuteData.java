package net.armcloud.paascenter.traffic.info.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 集群监控分钟维度聚合数据实体类
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("cluster_monitor_minute_data")
public class ClusterMonitorMinuteData {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集群编码
     */
    private String clusterCode;

    /**
     * 指标类型：ceph_pressure,write_rate,capacity_usage等
     */
    private String metricType;

    /**
     * 分钟时间点（精确到分钟）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date minuteTime;

    /**
     * 平均值
     */
    private Double avgValue;

    /**
     * 最大值
     */
    private Double maxValue;

    /**
     * 最小值
     */
    private Double minValue;

    /**
     * 原始数据点数量
     */
    private Integer dataCount;

    /**
     * 指标单位
     */
    private String metricUnit;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
