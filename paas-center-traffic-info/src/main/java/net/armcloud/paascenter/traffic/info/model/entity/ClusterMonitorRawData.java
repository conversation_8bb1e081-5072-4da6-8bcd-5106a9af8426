package net.armcloud.paascenter.traffic.info.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 集群监控原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("cluster_monitor_raw_data")
public class ClusterMonitorRawData {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集群编码
     */
    private String clusterCode;

    /**
     * 指标类型：ceph_pressure,write_rate,capacity_usage等
     */
    private String metricType;

    /**
     * 指标值
     */
    private Double metricValue;

    /**
     * 指标单位：%,MB/s,GB等
     */
    private String metricUnit;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;
}
