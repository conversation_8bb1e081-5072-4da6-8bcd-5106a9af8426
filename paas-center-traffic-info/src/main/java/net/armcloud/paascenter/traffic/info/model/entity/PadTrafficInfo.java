package net.armcloud.paascenter.traffic.info.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("pad_traffic_info")
public class PadTrafficInfo {

    private String padCode;
    private String padIp;
    private Long customerId;
    private Date createDay;

    private Long publicOut;
    private Long publicIn;
    private Long privateOut;
    private Long privateIn;
    private Long totalOut;
    private Long totalIn;
    private String msgId;
    private Date billingTime;
    private String dcCode;
    private Long statisticsBatchHour;

    private String createBy;
    private String updateBy;
    private Date updateTime;
    private Date createTime;

}