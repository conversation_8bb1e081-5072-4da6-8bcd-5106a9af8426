package net.armcloud.paascenter.traffic.info.redis.contstant;

/**
 * redis key的前缀
 */
public class KeyPrefix {
    public static final String LOCK = "LOCK:";
    //bmc管理的服务器列表
    public static final String BMC_SERVER_LIST = "BMC_SERVER_LIST";
    //  bmc管理的节点信息
    public static final String BMC_CARD_LIST = "BMC_CARD_LIST:";
    //bmc管理的服务器心跳错误次数
    public static final String BMC_SERVER_HEARTBEAT_ERROR_COUNT = "BMC_SERVER_HEARTBEAT_ERROR_COUNT:";
    //凌点认证token
    public static final String LING_DIAN_AUTH_TOKEN = "LING_DIAN_AUTH_TOKEN:";
    //auth-token 对应的socUrl
    public static final String LING_DIAN_TOKEN_SOC_URL = "LING_DIAN_TOKEN_SOC_URL:";
    //node 对应的socUrl
    public static final String LING_DIAN_NODE_SOC_URL = "LING_DIAN_NODE_SOC_URL:";

    public static final String CARD_TASK_INFO = "CARD_TASK_INFO:";
    //子任务对应的任务信息
    public static final String CARD_TASK_INFO_SUB_ID = "CARD_TASK_INFO_SUB_ID:";
    //子任务信息
    public static final String CARD_TASK_SUB_INFO = "CARD_TASK_SUB_INFO:";

    //节点断电重启任务
    public static final String CARD_POWER_RESTART = "CARD_POWER_RESTART:";


    //设置节点网络队列
    public static final String SET_CARD_NETWORK_ZSET = "SET_CARD_NETWORK_ZSET";
    //节点下电队列
    public static final String POWER_OFF_ZSET = "POWER_OFF_ZSET";
    //节点下电结果查询队列
    public static final String POWER_OFF_QUERY_ZSET = "POWER_OFF_QUERY_ZSET";
    //节点上电队列
    public static final String POWER_ON_ZSET = "POWER_ON_ZSET";
    //节点上电结果查询队列
    public static final String POWER_ON_QUERY_ZSET = "POWER_ON_QUERY_ZSET";
    //节点重置队列
    public static final String CARD_RESET_ZSET = "CARD_RESET_ZSET";
    //节点重置查询结果队列
    public static final String CARD_RESET_QUERY_ZSET = "CARD_RESET_QUERY_ZSET";


    //服务器ip列表
    public static final String SERVER_IP_LIST = "server-ip-list";
    //服务器ip对应的节点ip列表
    public static final String SERVER_DEVICE_IP = "server-device-ip:";
}