package net.armcloud.paascenter.traffic.info.rocketmq;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.facade.DeviceInternalFacade;
import net.armcloud.paascenter.traffic.info.internal.vo.DeviceVO;
import net.armcloud.paascenter.traffic.info.model.dto.DiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.dto.InsertDiskInfoDTO;
import net.armcloud.paascenter.traffic.info.redis.service.RedisService;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.traffic.info.service.DiskParserContext;
import net.armcloud.paascenter.traffic.info.service.IDiskInfoService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.cbs-traffic-disk-data}_consumer", topic = "${producer-topic.cbs-traffic-disk-data}")
public class CbsTrafficDiskConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private DeviceInternalFacade deviceInternalFacade;
    @Resource
    private IDiskInfoService iDiskInfoService;
    @Resource
    RedisService redisService;

    @Override
    public void onMessage(MessageView messageView) {
        try {
            String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
            // 尝试解析为数组
            List<DiskInfoDTO> dtoList;
            try {
                // 尝试将消息解析为数组
                dtoList = JSON.parseArray(str, DiskInfoDTO.class);
                log.info("成功解析消息为数组，数组大小: {}", dtoList.size());
            } catch (Exception e) {
                // 如果解析数组失败，尝试解析为单个对象（向后兼容）
                log.info("消息不是数组格式，尝试解析为单个对象");
                DiskInfoDTO dto = JSON.parseObject(str, DiskInfoDTO.class);
                dtoList = Collections.singletonList(dto);
            }
            
            // 处理列表中的每个消息
            for (DiskInfoDTO dto : dtoList) {
                processMessage(dto);
            }
        } catch (Exception e) {
            log.error("CbsTrafficDiskConsumerException-消费异常", e);
        }
    }
    
    /**
     * 处理单个磁盘信息消息
     * @param dto 磁盘信息DTO对象
     */
    private void processMessage(DiskInfoDTO dto) {
        try {
            // 使用缓存优先的设备信息获取方法
            DeviceVO deviceVo = getDeviceInfo(dto.getDeviceIp());
            InsertDiskInfoDTO insertDiskInfoDTO = new InsertDiskInfoDTO();
            insertDiskInfoDTO.setDcId(dto.getDcId());
            insertDiskInfoDTO.setDateTime(dto.getDateTime());
            insertDiskInfoDTO.setDeviceIp(dto.getDeviceIp());
            insertDiskInfoDTO.setDataInfo(dto.getDataInfo());
            insertDiskInfoDTO.setCreateTime(new Date());
            if (deviceVo != null) {
                insertDiskInfoDTO.setCustomerId(deviceVo.getCustomerId());
                insertDiskInfoDTO.setCustomerName(deviceVo.getCustomerName());
                insertDiskInfoDTO.setDeviceCreateTime(deviceVo.getCreateTime());
            } else {
                insertDiskInfoDTO.setCustomerId(0L);
                insertDiskInfoDTO.setCustomerName("");
                insertDiskInfoDTO.setDeviceCreateTime("");
            }
            if (CollectionUtil.isNotEmpty(dto.getTop10CpuInfo())) {
                insertDiskInfoDTO.setStrTop10CpuInfo(JSON.toJSONString(dto.getTop10CpuInfo()));
            }

            if (CollectionUtil.isNotEmpty(dto.getTop10IOInfo())){
                insertDiskInfoDTO.setStrTop10IOInfo(JSON.toJSONString(dto.getTop10IOInfo()));
            }

            String dataToProcess = dto.getDataInfo();
            boolean isJsonFormat = false;
            String cid = "";
            // 尝试判断是否为JSON格式
            try {
                if (dto.getDataInfo().trim().startsWith("{")) {
                    // 可能是JSON格式，尝试解析
                    com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(dto.getDataInfo());
                    if (jsonObject != null && jsonObject.containsKey("mmcdata")) {
                        // 获取CID用于判断是闪迪还是佰维
                        if (jsonObject.containsKey("cid")) {
                            cid = jsonObject.getString("cid");
                        }
                        // 这是新格式，从mmcdata获取二进制数据
                        dataToProcess = jsonObject.getString("mmcdata");
                        isJsonFormat = true;
                    }
                }
            } catch (Exception e) {
                // 解析JSON失败，说明是旧格式，继续使用原始数据
                log.warn("尝试解析JSON格式时出错，将按旧格式处理", e);
            }

            // 按行分割
            String[] lines = dataToProcess.split("\n");
            StringBuilder cleanedData = new StringBuilder();
            // 根据格式决定是否跳过第一行
            int startLine = isJsonFormat ? 0 : 1;
            //从第二行开始处理
            for (int i = startLine; i < lines.length; i++) {
                //去掉行首尾空格，并拼接为单行
                cleanedData.append(lines[i].trim()).append(" ");
            }

            // 去掉首尾空格
            String processedData = cleanedData.toString().trim();
            // 根据空格切割
            String[] elements = processedData.split("\\s+");
            DiskParserContext parserContext = new DiskParserContext();
            boolean parseResult = parserContext.parseDisk(cid, elements, insertDiskInfoDTO);
            if(parseResult){
                iDiskInfoService.batchSave(insertDiskInfoDTO);
            }else{
                log.info("insertDiskInfoDTO解析后异常数据：{}", JSON.toJSONString(insertDiskInfoDTO));
            }
        } catch (Exception e) {
            log.error("处理单个消息时发生异常，deviceIp={}", dto.getDeviceIp(), e);
        }
    }

    public static String padSingleDigit(String input) {
        return input.length() == 1 ? "0" + input : input;
    }
    /**
     * 获取设备信息,优先从缓存中获取
     * @param deviceIp 设备IP地址
     * @return 设备信息
     */
    private DeviceVO getDeviceInfo(String deviceIp){
        try {
            // 设置Redis缓存键
            String cacheKey = "paas-center-traffic-info:device:info:" + deviceIp;
            // 从缓存中获取设备信息
            Object cacheObject =  redisService.getCacheObject(cacheKey);
            DeviceVO deviceVO = null;
            if(Objects.nonNull(cacheObject)){
                deviceVO = JSON.parseObject(cacheObject.toString(), DeviceVO.class);
            }
            if (Objects.nonNull(deviceVO)) {
                return deviceVO;
            }

            // 缓存未命中，从服务获取
            Result<DeviceVO> deviceVOResult = deviceInternalFacade.selectCustomerByDeviceIp(deviceIp);
            if (deviceVOResult != null && Result.isSuccess(deviceVOResult) && deviceVOResult.getData() != null) {
                deviceVO = deviceVOResult.getData();
                // 存入缓存，设置过期时间为30分钟
                redisService.setCacheObject(cacheKey, JSON.toJSONString(deviceVO), 30L, TimeUnit.MINUTES);
                log.info("设备信息已缓存: {}", deviceIp);
                return deviceVO;
            }
            return null;
        } catch (Exception e) {
            log.error("获取设备信息异常: {}", deviceIp, e);
            return null;
        }
    }
}
