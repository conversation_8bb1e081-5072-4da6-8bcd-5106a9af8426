package net.armcloud.paascenter.traffic.info.rocketmq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.entity.traffic.DeviceSystemConfigData;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.facade.DeviceInternalFacade;
import net.armcloud.paascenter.traffic.info.model.dto.DeviceSystemConfigDataDTO;
import net.armcloud.paascenter.traffic.info.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.traffic.info.service.IDeviceSystemConfigDataService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import net.armcloud.paascenter.traffic.info.redis.lock.RedissonDistributedLock;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.device_system_config_data}_consumer", topic = "${producer-topic.device_system_config_data}")
public class DeviceSystemConfigDataTopicConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private DeviceInternalFacade deviceInternalFacade;
    @Resource
    private IDeviceSystemConfigDataService deviceSystemConfigDataService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("》》》》》》》》》》》DeviceSystemConfigDataTopicConsumer message:{}", str);
        DeviceSystemConfigDataDTO dto = JSON.parseObject(str, DeviceSystemConfigDataDTO.class);
        String key = RedisKeyPrefix.PAD_SYSTEM_CONFIG_DATA_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.mustLocked(key);
        try {
            Result<Device> result = deviceInternalFacade.selectByDeviceIp(dto.getDeviceIp());
            if (result.getCode() == Result.SUCCESS) {
                if(ObjectUtil.isNull(result.getData())){
                    return;
                }
                DeviceSystemConfigData save = new DeviceSystemConfigData();
                save.setDeviceCode(result.getData().getDeviceCode());
                save.setDeviceIp(result.getData().getDeviceIp());
                save.setCreateDay(new Date());
                if (Objects.nonNull(dto.getSystemData())){
                    save.setData(JSON.toJSONString(dto.getSystemData()));
                }
                save.setMsgId(messageView.getMessageId().toString());
                save.setBillingTime(dto.getBillingTime());
                save.setCreateBy("consumer");
                deviceSystemConfigDataService.saveDeviceSystemConfig(save);
            } else {
                throw new RuntimeException("padTrafficDataTopicConsumer error");
            }
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }
}
