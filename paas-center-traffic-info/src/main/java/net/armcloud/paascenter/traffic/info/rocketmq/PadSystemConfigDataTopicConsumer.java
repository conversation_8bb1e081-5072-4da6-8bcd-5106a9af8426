package net.armcloud.paascenter.traffic.info.rocketmq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.dto.api.PadIpDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.traffic.PadSystemConfigData;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.facade.PadInternalFacade;
import net.armcloud.paascenter.traffic.info.model.dto.PadSystemConfigDataDTO;
import net.armcloud.paascenter.traffic.info.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.traffic.info.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.traffic.info.service.IPadSystemConfigDataService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.pad_system_config_data}_consumer", topic = "${producer-topic.pad_system_config_data}")
public class PadSystemConfigDataTopicConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private PadInternalFacade padInternalFacade;
    @Resource
    private IPadSystemConfigDataService padSystemConfigDataService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("》》》》》》》》》》》PadSystemConfigDataTopicConsumer message:{}", str);
        PadSystemConfigDataDTO dto = JSON.parseObject(str, PadSystemConfigDataDTO.class);

        String key = RedisKeyPrefix.PAD_SYSTEM_CONFIG_DATA_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.mustLocked(key);
        try {
            PadIpDTO padIpDTO = new PadIpDTO();
            padIpDTO.setPadOutCode(dto.getPadOutCode());
            padIpDTO.setIp(dto.getPadIp());
            Result<Pad> result = padInternalFacade.getPadByOutCodeAndIp(padIpDTO);
            if (result.getCode() == Result.SUCCESS) {
                if(ObjectUtil.isNull(result.getData())){
                    return;
                }
                PadSystemConfigData save = new PadSystemConfigData();
                save.setPadCode(result.getData().getPadCode());
                save.setPadIp(result.getData().getPadIp());
                save.setCreateDay(new Date());
                if (Objects.nonNull(dto.getSystemData())){
                    save.setData(JSON.toJSONString(dto.getSystemData()));
                }
                save.setMsgId(messageView.getMessageId().toString());
                save.setBillingTime(dto.getBillingTime());
                save.setCreateBy("consumer");
                padSystemConfigDataService.savePadSystemConfig(save);
            } else {
                throw new RuntimeException("padTrafficDataTopicConsumer error");
            }
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }
}
