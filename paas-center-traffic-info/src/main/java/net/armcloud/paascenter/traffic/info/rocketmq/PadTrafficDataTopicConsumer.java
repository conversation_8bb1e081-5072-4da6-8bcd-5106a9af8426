package net.armcloud.paascenter.traffic.info.rocketmq;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.model.dto.TrafficDataDTO;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.traffic.info.service.IPadTrafficInfoService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.pad-traffic-data}_consumer", topic = "${producer-topic.pad-traffic-data}")
public class PadTrafficDataTopicConsumer implements AliRocketMQListener<MessageView> {
    @Autowired
    @Qualifier("padTrafficInfoServiceImpl")
    private IPadTrafficInfoService padTrafficInfoService;


    @Override
    public void onMessage(MessageView messageView) {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("》》》》》》》》》》》padTrafficDataTopicConsumer message:{}", str);
        TrafficDataDTO dto = JSON.parseObject(str, TrafficDataDTO.class);
        padTrafficInfoService.handleTrafficData(messageView.getMessageId().toString(), dto);
    }
}
