package net.armcloud.paascenter.traffic.info.rocketmq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.traffic.PadTrafficInfo;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.pad-traffic-info-data}_consumer", topic = "${producer-topic.pad-traffic-info-data}")
public class PadTrafficInfoConsumer implements AliRocketMQListener<MessageView> {

    private final PadTrafficInfoClickHouseService padTrafficInfoClickHouseService;

    public PadTrafficInfoConsumer(PadTrafficInfoClickHouseService padTrafficInfoClickHouseService) {
        this.padTrafficInfoClickHouseService = padTrafficInfoClickHouseService;
    }

    @Override
    public void onMessage(MessageView messageView) {
        try {
            String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
            if (str.contains("[")) {
                List<PadTrafficInfo> infos = JSON.parseArray(str, PadTrafficInfo.class);
                for (PadTrafficInfo dto : infos) {
                    padTrafficInfoClickHouseService.batchSave(dto);
                }
            } else {
                PadTrafficInfo dto = JSON.parseObject(str, PadTrafficInfo.class);
                padTrafficInfoClickHouseService.batchSave(dto);
            }
        } catch (Exception e) {
            log.error("PadTrafficInfoConsumer-消费异常拉", e);
        }
    }

}