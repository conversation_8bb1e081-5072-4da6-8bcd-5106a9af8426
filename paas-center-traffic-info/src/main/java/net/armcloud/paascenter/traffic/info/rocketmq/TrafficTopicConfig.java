package net.armcloud.paascenter.traffic.info.rocketmq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "producer-topic")
public class TrafficTopicConfig {
    /**
     * armcloud-paas-实例流量数据
     */
    private String padTrafficData;


    /**
     * cbs上报磁盘信息
     */
    private String cbsTrafficDiskData;
}
