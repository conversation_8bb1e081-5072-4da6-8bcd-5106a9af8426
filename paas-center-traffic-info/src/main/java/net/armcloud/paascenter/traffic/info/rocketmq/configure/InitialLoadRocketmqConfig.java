package net.armcloud.paascenter.traffic.info.rocketmq.configure;

import net.armcloud.paascenter.traffic.info.rocketmq.properties.AliRocketmqConfig;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.StaticSessionCredentialsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class InitialLoadRocketmqConfig {
    @Autowired
    private AliRocketmqConfig rocketmqConfig;

    @Bean
    public ClientServiceProvider alimqClientServiceProvider() {
        return ClientServiceProvider.loadService();
    }

    @Bean
    public ClientConfiguration alimqClientConfiguration() {
        //在RocketMQ实例详情页面获取用户名和密码
        StaticSessionCredentialsProvider staticSessionCredentialsProvider = new StaticSessionCredentialsProvider(rocketmqConfig.getAccessKey(), rocketmqConfig.getAccessSecret());
        return ClientConfiguration.newBuilder().setEndpoints(rocketmqConfig.getEndpoints()).setCredentialProvider(staticSessionCredentialsProvider).build();
    }
}
