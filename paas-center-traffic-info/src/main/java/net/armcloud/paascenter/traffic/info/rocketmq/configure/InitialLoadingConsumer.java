package net.armcloud.paascenter.traffic.info.rocketmq.configure;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.traffic.info.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.traffic.info.rocketmq.concurrent.CommonThreadPoolExecutors;
import net.armcloud.paascenter.traffic.info.rocketmq.properties.AliRocketmqConfig;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.concurrent.ExecutorService;

import static org.springframework.aop.support.AopUtils.getTargetClass;

@Slf4j
@Service
public class InitialLoadingConsumer implements CommandLineRunner {
    private final AliRocketmqConfig rocketmqConfig;
    int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
    final ExecutorService executor = CommonThreadPoolExecutors.newThreadPoolExecutor(corePoolSize + 1, corePoolSize * 2, "rocketMqConsumer");

    @Override
    public void run(String... args) {
        log.info("start initializing rocketmq consumer...");
        if (Boolean.FALSE.equals(rocketmqConfig.getEnableConsumer())) {
            log.info("disable rocketmq consumer...");
            return;
        }

        SpringUtil.getBeansOfType(AliRocketMQListener.class).values().forEach(listener -> {
            AliRocketMQMsgListener annotation = getTargetClass(listener).getAnnotation(AliRocketMQMsgListener.class);
            if (annotation == null) {
                log.error("The listener {} is not annotated with AliRocketMQMsgListener", listener.getClass().getName());
                return;
            }

            executor.submit(() -> {
                // 解析占位符
                Environment environment = SpringUtil.getBean(Environment.class);
                String consumerGroup = environment.resolvePlaceholders(annotation.consumerGroup());
                String topic = environment.resolvePlaceholders(annotation.topic());
                log.info("start initialize RocketMQ listener for topic {} ", topic);
                ClientServiceProvider clientServiceProvider = SpringUtil.getBean(ClientServiceProvider.class);
                ClientConfiguration clientConfiguration = SpringUtil.getBean(ClientConfiguration.class);
                FilterExpression filterExpression = new FilterExpression(annotation.tag(), FilterExpressionType.TAG);
                try {
                    clientServiceProvider.newPushConsumerBuilder().setClientConfiguration(clientConfiguration)
                            .setClientConfiguration(clientConfiguration)
                            .setConsumerGroup(consumerGroup)
                            .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
                            .setMessageListener(messageView -> {
                                try {
                                    log.info("topic:{} consumerGroup:{} start consumer message:{}", topic, consumerGroup, messageView.getMessageId().toString());
                                    listener.onMessage(messageView);
                                    return ConsumeResult.SUCCESS;
                                } catch (Exception e) {
                                    log.error("onMessage error>>>>messageView:{}", JSON.toJSONString(messageView), e);
                                    return ConsumeResult.FAILURE;
                                }
                            })
                            .build();
                } catch (Exception e) {
                    log.error("ClientException error>>>>", e);
                    throw new RuntimeException(e);
                }
            });
        });
    }

    public InitialLoadingConsumer(AliRocketmqConfig rocketmqConfig) {
        this.rocketmqConfig = rocketmqConfig;
    }

}
