package net.armcloud.paascenter.traffic.info.rocketmq.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "rocketmq")
public class AliRocketmqConfig {
    /**
     * 连接地址
     */
    private String endpoints;

    /**
     * ak
     */
    private String accessKey;

    /**
     * sk
     */
    private String accessSecret;

    /**
     * 是否开启消费者
     */
    private Boolean enableConsumer;

}
