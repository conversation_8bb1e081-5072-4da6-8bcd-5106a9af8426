package net.armcloud.paascenter.traffic.info.rocketmq.support;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DefaultRocketMqProducerWrapper implements IRocketMqProducerWrapper {

    private final RocketMQTemplate rocketMQTemplate;

    public DefaultRocketMqProducerWrapper(RocketMQTemplate rocketMQTemplate) {
        this.rocketMQTemplate = rocketMQTemplate;
    }

    public String producerNormalMessage(String topic, String msg) {
        return producerNormalMessage(topic, null, msg);
    }

    /**
     * 发送普通消息
     *
     * @param topic 消息主题
     * @param tag   消息标签
     * @param msg   消息主体
     * @return messageId 消息Id
     */
    @Override
    public String producerNormalMessage(String topic, String tag, String msg) {
        if (StringUtils.isAnyBlank(topic, msg)) {
            if (StringUtils.isBlank(topic)) {
                throw new RuntimeException("topic is blank");
            }
            return "";
        }
        if (StringUtils.isNotBlank(tag)) {
            topic = topic + ":" + tag;
        }
        rocketMQTemplate.sendOneWay(topic, msg);
        log.info("send rocket mq normal message >>>> topic:{}", topic);
        return StringUtils.EMPTY;
    }

    @Override
    public String producerOrderlyMessage(String topic, String hashKey, String msg) {
        final SendResult result = rocketMQTemplate.syncSendOrderly(topic, msg, hashKey);
        String msgId = result.getMsgId();
        /*log.info("send rocket mq order message >>>> topic:{},hashKey:{},messageId:{}", topic, hashKey, msgId);*/
        return msgId;
    }
}
