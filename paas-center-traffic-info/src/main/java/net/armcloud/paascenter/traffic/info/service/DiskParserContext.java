package net.armcloud.paascenter.traffic.info.service;

import net.armcloud.paascenter.traffic.info.model.dto.InsertDiskInfoDTO;
import net.armcloud.paascenter.traffic.info.service.impl.BiwinIDiskParserImpl;
import net.armcloud.paascenter.traffic.info.service.impl.SandiskIDiskParser;

import java.util.ArrayList;
import java.util.List;

public class DiskParserContext {

    private final List<IDiskParserStrategy> strategies = new ArrayList<>();

    public DiskParserContext() {
        strategies.add(new BiwinIDiskParserImpl());
        strategies.add(new SandiskIDiskParser());
    }

    public boolean parseDisk(String cid, String[] elements, InsertDiskInfoDTO dto) {
        for (IDiskParserStrategy strategy : strategies) {
            if (strategy.supports(cid)) {
                return strategy.parse(elements, dto);
            }
        }
        throw new UnsupportedOperationException("未找到支持的磁盘解析策略，CID: " + cid);
    }
}

