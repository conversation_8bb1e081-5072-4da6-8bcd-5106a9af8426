package net.armcloud.paascenter.traffic.info.service;

/**
 * 集群监控数据聚合服务接口
 * 
 * <AUTHOR>
 */
public interface IClusterMonitorAggregationService {

    /**
     * 聚合指定时间范围的原始数据到分钟表
     * 
     * @param minutesAgo 多少分钟前的数据
     * @return 聚合的数据条数
     */
    int aggregateRawDataToMinute(int minutesAgo);

    /**
     * 聚合最近的原始数据到分钟表（默认聚合2分钟前的数据）
     * 
     * @return 聚合的数据条数
     */
    int aggregateRecentData();
}
