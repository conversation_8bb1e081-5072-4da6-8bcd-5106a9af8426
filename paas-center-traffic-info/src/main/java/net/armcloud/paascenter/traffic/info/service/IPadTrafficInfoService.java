package net.armcloud.paascenter.traffic.info.service;


import net.armcloud.paascenter.common.model.entity.traffic.PadTrafficInfo;
import net.armcloud.paascenter.traffic.info.model.dto.DiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.dto.TrafficDataDTO;

/**
 * <AUTHOR>
 */
public interface IPadTrafficInfoService {

    /**
     * 发送流量数据到mq
     *
     * @param param dto
     * @return Boolean
     */
    Boolean sendMessage(TrafficDataDTO param);

    /**
     * 保存流量数据
     *
     * @param param
     * @return
     */
    Boolean saveTrafficInfoService(String tableName, PadTrafficInfo param);

    /**
     * 处理流量数据消息
     *
     * @param dto
     */
    Boolean handleTrafficData(String msgId, TrafficDataDTO dto);

    /**
     * cbs上报磁盘信息
     *
     * @param dto
     */
    void reportDiskInfo(DiskInfoDTO dto) throws Exception;
}
