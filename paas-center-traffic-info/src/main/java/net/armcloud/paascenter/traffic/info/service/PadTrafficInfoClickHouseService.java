package net.armcloud.paascenter.traffic.info.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.traffic.PadTrafficInfo;
import net.armcloud.paascenter.traffic.info.common.buffer.SafeMemBuffer;
import net.armcloud.paascenter.traffic.info.mapper.clickhouse.PadTrafficInfoClickhouseMapper;
import net.armcloud.paascenter.traffic.info.model.dto.InsertDiskInfoDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PadTrafficInfoClickHouseService {

    @Value("${buffer.clickhouse.batchSize:100}")
    private int batchSize;

    @Value("${buffer.clickhouse.flushInterval:10}")
    private int flushIntervalSeconds;

    @Value("${buffer.clickhouse.capacity:10000}")
    private int bufferCapacity;

    private final PadTrafficInfoClickhouseMapper padTrafficInfoClickhouseMapper;

    public PadTrafficInfoClickHouseService(PadTrafficInfoClickhouseMapper padTrafficInfoClickhouseMapper) {
        this.padTrafficInfoClickhouseMapper = padTrafficInfoClickhouseMapper;
    }

    private SafeMemBuffer<Map<String, Object>> diskInfoBuffer;

    @PostConstruct
    public void init() {
        log.info("初始化ClickHouse磁盘信息缓冲区, batchSize={}, flushInterval={}s, capacity={}",
                batchSize, flushIntervalSeconds, bufferCapacity);

        diskInfoBuffer = SafeMemBuffer.<Map<String, Object>>builder()
                .capacity(bufferCapacity)
                .batchSize(1000)
                .flushInterval(Duration.ofSeconds(1))
                .flushAction(this::saveBatch)
                .onError(e -> log.error("写入ClickHouse失败", e))
                .maxRetries(3)
                .initialRetryDelay(Duration.ofSeconds(1))
                .retryDelayMultiplier(2.0)
                .maxRetryDelay(Duration.ofMinutes(2))
                .deadLetterHandler(this::handleDeadLetters)
                .build();
    }

    @PreDestroy
    public void destroy() {
        if (diskInfoBuffer != null) {
            log.info("关闭ClickHouse磁盘信息缓冲区，最后一次刷新");
            diskInfoBuffer.close();
        }
    }

    public void save(PadTrafficInfo save) {
        try {
            log.info("save to clickhouse {}", save);
            net.armcloud.paascenter.traffic.info.model.entity.PadTrafficInfo entity =
                    new net.armcloud.paascenter.traffic.info.model.entity.PadTrafficInfo();

            BeanUtils.copyProperties(save, entity);

            int insert = padTrafficInfoClickhouseMapper.batchInsert(Collections.singletonList(entity));
            log.info("Insert {} records into database", insert);
        } catch (Exception e) {
            log.error("save to clickhouse failed", e);
        }
    }

    public void batchSave(PadTrafficInfo dto) {
        try {
            Map<String, Object> record = BeanUtil.beanToMap(dto);
            boolean added = diskInfoBuffer.add(record);
            if (!added) {
                log.warn("添加到缓冲区失败，可能缓冲区已满: {}", dto.getPadIp());
            }
        } catch (Exception e) {
            log.error("处理信息时发生错误", e);
        }
    }

    private void saveBatch(List<Map<String, Object>> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        try {
            padTrafficInfoClickhouseMapper.batchInsert2(records);
            log.info("批量写入ClickHouse成功: {} 条记录", records.size());
        } catch (Exception e) {
            log.error("批量写入ClickHouse失败: {}", e.getMessage());
            if (records.size() > 1) {
                log.info("开始进行单条记录插入...");
                for (Map<String, Object> record : records) {
                    try {
                        // 单条插入
                        List<Map<String, Object>> singleRecord = Collections.singletonList(record);
                        padTrafficInfoClickhouseMapper.batchInsert2(singleRecord);
                    } catch (Exception ex) {
                        try {
                            log.warn("单条记录插入失败: {}, 记录内容: {}",
                                    ex.getMessage(), JSON.toJSONString(record));
                        } catch (Exception jsonEx) {
                            log.warn("单条记录插入失败且无法序列化记录内容");
                        }
                    }
                }
            }
        }
    }

    private void handleDeadLetters(List<Map<String, Object>> deadLetters) {
        log.error("handleDeadLetters:磁盘信息写入ClickHouse失败: {}", deadLetters.size());
    }

}