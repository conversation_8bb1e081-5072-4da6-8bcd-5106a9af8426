package net.armcloud.paascenter.traffic.info.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.traffic.PadTrafficInfo;
import net.armcloud.paascenter.traffic.info.common.buffer.SafeMemBuffer;
import net.armcloud.paascenter.traffic.info.mapper.clickhouse.PadTrafficInfoClickhouseMapper;
import net.armcloud.paascenter.traffic.info.rocketmq.support.IRocketMqProducerWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;

@Slf4j
@Service
public class PadTrafficInfoSendMsgService {

    @Resource
    private IRocketMqProducerWrapper rocketMqProducerWrapper;

    private SafeMemBuffer<Map<String, Object>> diskInfoBuffer;

    @Value("${producer-topic.pad-traffic-info-data}")
    private String padTrafficInfoTopic;

    @PostConstruct
    public void init() {
        diskInfoBuffer = SafeMemBuffer.<Map<String, Object>>builder()
                .capacity(1000)
                .batchSize(20)
                .flushInterval(Duration.ofSeconds(1))
                .flushAction(this::saveBatch)
                .onError(e -> log.error("批量发送消息失败", e))
                .maxRetries(3)
                .initialRetryDelay(Duration.ofSeconds(1))
                .retryDelayMultiplier(2.0)
                .maxRetryDelay(Duration.ofMinutes(2))
                .deadLetterHandler(this::handleDeadLetters)
                .build();
    }

    @PreDestroy
    public void destroy() {
        if (diskInfoBuffer != null) {
            log.info("关闭缓冲区，最后一次刷新");
            diskInfoBuffer.close();
        }
    }

    public void batchSave(PadTrafficInfo msg) {
        try {
            Map<String, Object> record = BeanUtil.beanToMap(msg);
            boolean added = diskInfoBuffer.add(record);
            if (!added) {
                log.warn("添加到缓冲区失败，可能缓冲区已满");
            }
        } catch (Exception e) {
            log.error("处理信息时发生错误", e);
        }
    }

    private void saveBatch(List<Map<String, Object>> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        String jsonStr = JSONUtil.toJsonStr(records);
        log.info("batch send msg size: {}, json: {}", records.size(), jsonStr);
        rocketMqProducerWrapper.producerNormalMessage(padTrafficInfoTopic,null, jsonStr);
    }

    private void handleDeadLetters(List<Map<String, Object>> deadLetters) {
        log.error("handleDeadLetters: {}", deadLetters.size());
    }

}