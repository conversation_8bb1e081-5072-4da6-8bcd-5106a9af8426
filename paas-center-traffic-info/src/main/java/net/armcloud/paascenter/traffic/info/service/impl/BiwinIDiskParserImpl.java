package net.armcloud.paascenter.traffic.info.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.model.dto.InsertDiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.enums.EndianType;
import net.armcloud.paascenter.traffic.info.service.IDiskParserStrategy;
import net.armcloud.paascenter.traffic.info.util.DiskParseUtil;

@Slf4j
public class BiwinIDiskParserImpl implements IDiskParserStrategy {

    private static final int CHWDZ_OFFSET = 8;
    private static final int SLC_OFFSET = 24;
    private static final int TLC_OFFSET = 36;

    @Override
    public boolean parse(String[] elements, InsertDiskInfoDTO dto) {
        try{
            long chwdzRaw = DiskParseUtil.parseByEndian(elements, CHWDZ_OFFSET, 8, EndianType.LITTLE_ENDIAN);
            dto.setChwdz((chwdzRaw * 512) / (100 * 1024 * 1024));

            // SLC 计算
            long slcRaw = DiskParseUtil.parseByEndian(elements, SLC_OFFSET, 4, EndianType.LITTLE_ENDIAN);
            long slcPercent = calcUsedLifePercent(slcRaw, 50000);
            dto.setSlc(slcPercent);

            // TLC 计算
            long tlcRaw = DiskParseUtil.parseByEndian(elements, TLC_OFFSET, 4, EndianType.LITTLE_ENDIAN);
            long tlcPercent = calcUsedLifePercent(tlcRaw, 3000);
            dto.setTlc(tlcPercent);
            return true;
        }catch(Exception e){
            log.error("解析BiwinI磁盘信息失败", e);
            return false;
        }
    }
    /**
     * 计算使用寿命百分比，递增趋势（用于设备老化/消耗趋势监控）
     * @param usedCount 当前平均擦写次数
     * @param maxLife 设计最大寿命次数
     * @return 已使用百分比 (0~100)
     */
    public static long calcUsedLifePercent(long usedCount, long maxLife) {
        if (usedCount <= 0) {
            return 0L;
        }
        if (usedCount >= maxLife) {
            return 100L;
        }
        return (usedCount * 100) / maxLife;
    }


    @Override
    public boolean supports(String cid) {
        return cid != null && cid.startsWith("ab");
    }
}

