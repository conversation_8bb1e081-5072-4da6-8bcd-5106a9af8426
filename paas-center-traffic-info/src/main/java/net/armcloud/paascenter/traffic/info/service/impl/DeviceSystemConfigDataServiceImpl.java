package net.armcloud.paascenter.traffic.info.service.impl;

import net.armcloud.paascenter.common.model.entity.traffic.DeviceSystemConfigData;
import net.armcloud.paascenter.traffic.info.mapper.traffic.DeviceSystemConfigDataMapper;
import net.armcloud.paascenter.traffic.info.service.IDeviceSystemConfigDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static net.armcloud.paascenter.traffic.info.constant.NumberConsts.*;


@Service
public class DeviceSystemConfigDataServiceImpl  implements IDeviceSystemConfigDataService {

    private final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Autowired
    private DeviceSystemConfigDataMapper deviceSystemConfigDataMapper;

    @PostConstruct
    private void init() {
        initBeforeTable();
        initTableCreateAndDelete();
    }

    @Override
    public Boolean saveDeviceSystemConfig(DeviceSystemConfigData param) {
        String tableSuffix = new SimpleDateFormat("yyyyMMdd").format(param.getBillingTime());
        param.setTableSuffix(tableSuffix);
        return deviceSystemConfigDataMapper.insertDeviceSystemConfig(param) > 0;
    }

    @Scheduled(cron = "0 0 12 * * ?")
    public void initTableCreateAndDelete() {
        // 获取当前日期及后一天的日期
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(ONE);
        String todayStr = today.format(yyyyMMdd);
        String tomorrowStr = tomorrow.format(yyyyMMdd);
        String[] dates = {todayStr, tomorrowStr};

        // 检查和创建需要的表
        for (String date : dates) {
            if (deviceSystemConfigDataMapper.checkTableExists(date) == ZERO) {
                deviceSystemConfigDataMapper.createTable(date);
            }
        }

        // 删除三天前的表
        LocalDate threeDaysAgo = today.minusDays(THREE);
        String threeDaysAgoStr = threeDaysAgo.format(yyyyMMdd);
        deviceSystemConfigDataMapper.dropTable(threeDaysAgoStr);
    }

    private void initBeforeTable() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(ONE);
        LocalDate beforeDay = today.minusDays(TWO);
        String yesterdayStr = yesterday.format(yyyyMMdd);
        String beforeDayStr = beforeDay.format(yyyyMMdd);
        String[] dates = {yesterdayStr, beforeDayStr};
        // 检查和创建需要的表
        for (String date : dates) {
            if (deviceSystemConfigDataMapper.checkTableExists(date) == ZERO) {
                deviceSystemConfigDataMapper.createTable(date);
            }
        }
    }
}
