package net.armcloud.paascenter.traffic.info.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.common.buffer.SafeMemBuffer;
import net.armcloud.paascenter.traffic.info.mapper.clickhouse.DiskInfoMapper;
import net.armcloud.paascenter.traffic.info.model.dto.InsertDiskInfoDTO;
import net.armcloud.paascenter.traffic.info.service.IDiskInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 云手机磁盘信息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RefreshScope
public class DiskInfoServiceImpl implements IDiskInfoService {

    private final DiskInfoMapper diskInfoMapper;

    @Value("${traffic.buffer.clickhouse.batchSize:1000}")
    private int batchSize;

    @Value("${traffic.buffer.clickhouse.flushInterval:2}")
    private int flushIntervalSeconds;

    @Value("${traffic.buffer.clickhouse.capacity:10000}")
    private int bufferCapacity;

    private SafeMemBuffer<Map<String, Object>> diskInfoBuffer;

    @PostConstruct
    public void init() {
        log.info("初始化ClickHouse磁盘信息缓冲区, batchSize={}, flushInterval={}s, capacity={}",
                batchSize, flushIntervalSeconds, bufferCapacity);

        diskInfoBuffer = SafeMemBuffer.<Map<String, Object>>builder()
                .capacity(bufferCapacity)
                .batchSize(batchSize)
                .flushInterval(Duration.ofSeconds(flushIntervalSeconds))
                .flushAction(this::saveBatch)
                .onError(e -> log.error("写入ClickHouse失败", e))
                .maxRetries(3)
                .initialRetryDelay(Duration.ofSeconds(1))
                .retryDelayMultiplier(2.0)
                .maxRetryDelay(Duration.ofMinutes(2))
                .deadLetterHandler(this::handleDeadLetters)
                .build();
    }

    @PreDestroy
    public void destroy() {
        if (diskInfoBuffer != null) {
            log.info("关闭ClickHouse磁盘信息缓冲区，最后一次刷新");
            diskInfoBuffer.close();
        }
    }

    /**
     * 保存磁盘信息到ClickHouse (带缓冲区)
     */
    @Override
    public void batchSave(InsertDiskInfoDTO diskInfoDTO) {
        try {
            Map<String, Object> record = convertToClickHouseRecord(diskInfoDTO);
            boolean added = diskInfoBuffer.add(record);
            if (!added) {
                log.warn("磁盘信息添加到缓冲区失败，可能缓冲区已满: {}", diskInfoDTO.getDeviceIp());
            }
        } catch (Exception e) {
            log.error("处理磁盘信息时发生错误", e);
        }
    }

    /**
     * 批量保存磁盘信息记录到ClickHouse
     */
    private void saveBatch(List<Map<String, Object>> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        try {
            diskInfoMapper.batchInsert(records);
            log.debug("批量写入ClickHouse成功: {} 条记录", records.size());
        } catch (Exception e) {
            log.error("批量写入ClickHouse失败: {}", e.getMessage());
            if (records.size() > 1) {
                log.info("开始进行单条记录插入...");
                for (Map<String, Object> record : records) {
                    try {
                        // 单条插入
                        List<Map<String, Object>> singleRecord = Collections.singletonList(record);
                        diskInfoMapper.batchInsert(singleRecord);
                    } catch (Exception ex) {
                        try {
                            log.warn("单条记录插入失败: {}, 记录内容: {}", 
                                     ex.getMessage(), JSON.toJSONString(record));
                        } catch (Exception jsonEx) {
                            log.warn("单条记录插入失败且无法序列化记录内容");
                        }
                    }
                }
            }
        }
    }

    /**
     * 对于多次重试后仍然失败的记录的处理
     */
    private void handleDeadLetters(List<Map<String, Object>> deadLetters) {
        log.error("handleDeadLetters:磁盘信息写入ClickHouse失败: {}", deadLetters.size());
        try {
            String deadLetterJson = JSON.toJSONString(deadLetters);
            log.error("handleDeadLetters:内容: {}", deadLetterJson);
        } catch (Exception e) {
            log.error("handleDeadLetters:处理失败", e);
        }
    }

    /**
     * 将磁盘信息对象转换为ClickHouse记录
     */
    private Map<String, Object> convertToClickHouseRecord(InsertDiskInfoDTO dto) {
        Map<String, Object> record = new HashMap<>();

        // 使用当前小时作为分区键
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter hourFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH");
        String logCreateDayHour = now.format(hourFormatter);

        // 基本字段
        record.put("log_create_day_hour", logCreateDayHour);

        // ClickHouse 只支持到秒的 DateTime 格式，去掉毫秒部分
        // 创建时间格式化为 ClickHouse 兼容的格式 (不带毫秒)
        Timestamp createTimeTs = new Timestamp(dto.getCreateTime().getTime());
        String createTimeFormatted = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(createTimeTs);
        record.put("create_time", createTimeFormatted);

        record.put("create_date", new Date(dto.getCreateTime().getTime()));
        record.put("timestamp", System.currentTimeMillis());

        // 事件相关字段
        record.put("event_id", "ARM_CLOUD_PASS_REQUEST_STATS");
        record.put("page", "paas_gateway");
        record.put("event_uid", "0");
        record.put("device_key", "0");
        record.put("version_code", "0");
        record.put("version_name", "0");
        record.put("model", "0");
        record.put("brand", "0");

        // 设备和数据字段
        record.put("dc_id", convertToInt(dto.getDcId(), 0));
        record.put("device_ip", StringUtils.isNotBlank(dto.getDeviceIp()) ? dto.getDeviceIp() : "0");
        record.put("data_info", StringUtils.isNotBlank(dto.getDataInfo()) ? dto.getDataInfo() : "0");
        record.put("device_create_time",dto.getDeviceCreateTime());
        // date_time 也需要格式化为 ClickHouse 兼容的格式
        if (dto.getDateTime() != null) {
            Timestamp dateTimeTs = new Timestamp(dto.getDateTime().getTime());
            String dateTimeFormatted = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateTimeTs);
            record.put("date_time", dateTimeFormatted);
        } else {
            record.put("date_time", createTimeFormatted);
        }

        // 处理可能为null的字符串字段
        record.put("str_top10_cpu_info", StringUtils.isNotBlank(dto.getStrTop10CpuInfo()) ? dto.getStrTop10CpuInfo() : "0");
        record.put("str_top10_io_info", StringUtils.isNotBlank(dto.getStrTop10IOInfo()) ? dto.getStrTop10IOInfo() : "0");
        record.put("chwdz", convertToLong(dto.getChwdz(), 0L));
        record.put("tlc", convertToLong(dto.getTlc(), 0L));
        record.put("slc", convertToLong(dto.getSlc(), 0L));

        // 客户信息
        record.put("customer_id", convertToLong(dto.getCustomerId(), 0L));
        record.put("customer_name", dto.getCustomerName() != null ? dto.getCustomerName() : "");

        return record;
    }

    /**
     * 安全地将对象转换为Long类型
     * @param value 需要转换的值
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的Long值
     */
    private Long convertToLong(Object value, Long defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).longValue();
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (strValue.isEmpty()) {
                    return defaultValue;
                }
                try {
                    return Long.parseLong(strValue);
                } catch (NumberFormatException e) {
                    // 尝试作为Double解析然后转Long
                    return (long) Double.parseDouble(strValue);
                }
            } else if (value instanceof Boolean) {
                return ((Boolean) value) ? 1L : 0L;
            }
            return Long.parseLong(String.valueOf(value));
        } catch (Exception e) {
            log.debug("值[{}]无法转换为Long，使用默认值[{}]", value, defaultValue);
            return defaultValue;
        }
    }

    /**
     * @param value 需要转换的值
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的Int值
     */
    private Integer convertToInt(Object value, Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (strValue.isEmpty()) {
                    return defaultValue;
                }
                try {
                    return Integer.parseInt(strValue);
                } catch (NumberFormatException e) {
                    // 尝试作为Double解析然后转Int
                    return (int) Double.parseDouble(strValue);
                }
            } else if (value instanceof Boolean) {
                return ((Boolean) value) ? 1 : 0;
            }
            return Integer.parseInt(String.valueOf(value));
        } catch (Exception e) {
            log.debug("值[{}]无法转换为Int，使用默认值[{}]", value, defaultValue);
            return defaultValue;
        }
    }
}