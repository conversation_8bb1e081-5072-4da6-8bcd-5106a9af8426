package net.armcloud.paascenter.traffic.info.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.traffic.PadSystemConfigData;
import net.armcloud.paascenter.traffic.info.constant.NumberConsts;
import net.armcloud.paascenter.traffic.info.mapper.traffic.PadSystemConfigDataMapper;
import net.armcloud.paascenter.traffic.info.service.IPadSystemConfigDataService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static net.armcloud.paascenter.traffic.info.constant.NumberConsts.*;


@Slf4j
@Service
public class PadSystemConfigDataServiceImpl implements IPadSystemConfigDataService {

    private final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Resource
    private PadSystemConfigDataMapper padSystemConfigDataMapper;

    @PostConstruct
    private void init() {
        initBeforeTable();
        initTableCreateAndDelete();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePadSystemConfig(PadSystemConfigData param) {
        String tableSuffix = new SimpleDateFormat("yyyyMMdd").format(param.getBillingTime());
        param.setTableSuffix(tableSuffix);

        if (padSystemConfigDataMapper.checkTableExists(tableSuffix) > ZERO) {
            return padSystemConfigDataMapper.insertPadSystemConfig(param) > NumberConsts.ZERO;
        }
        log.error("表不存在，请先创建表,param={}", JSON.toJSONString(param));
        return false;
    }

    @Scheduled(cron = "0 0 11 * * ?")
    public void initTableCreateAndDelete() {
        // 获取当前日期及后一天的日期
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(ONE);
        String todayStr = today.format(yyyyMMdd);
        String tomorrowStr = tomorrow.format(yyyyMMdd);
        String[] dates = {todayStr, tomorrowStr};

        // 检查和创建需要的表
        for (String date : dates) {
            if (padSystemConfigDataMapper.checkTableExists(date) == ZERO) {
                padSystemConfigDataMapper.createTable(date);
            }
        }

        // 删除三天前的表
        LocalDate threeDaysAgo = today.minusDays(THREE);
        String threeDaysAgoStr = threeDaysAgo.format(yyyyMMdd);
        padSystemConfigDataMapper.dropTable(threeDaysAgoStr);
    }

    private void initBeforeTable() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(ONE);
        LocalDate beforeDay = today.minusDays(TWO);
        String yesterdayStr = yesterday.format(yyyyMMdd);
        String beforeDayStr = beforeDay.format(yyyyMMdd);
        String[] dates = {yesterdayStr, beforeDayStr};
        // 检查和创建需要的表
        for (String date : dates) {
            if (padSystemConfigDataMapper.checkTableExists(date) == ZERO) {
                padSystemConfigDataMapper.createTable(date);
            }
        }
    }
}
