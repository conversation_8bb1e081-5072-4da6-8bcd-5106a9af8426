package net.armcloud.paascenter.traffic.info.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.comms.LocalConst;
import net.armcloud.paascenter.common.model.dto.api.PadIpDTO;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.traffic.PadTrafficInfo;
import net.armcloud.paascenter.traffic.info.common.buffer.SafeMemBuffer;
import net.armcloud.paascenter.traffic.info.constant.NumberConsts;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.domain.entity.NetStoragePadUnitDetail;
import net.armcloud.paascenter.traffic.info.internal.facade.DCInternalFacade;
import net.armcloud.paascenter.traffic.info.internal.facade.DeviceInternalFacade;
import net.armcloud.paascenter.traffic.info.internal.facade.PadInternalFacade;
import net.armcloud.paascenter.traffic.info.mapper.paas.NetStoragePadUnitDetailMapper;
import net.armcloud.paascenter.traffic.info.mapper.traffic.PadTrafficInfoMapper;
import net.armcloud.paascenter.traffic.info.model.dto.DiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.dto.DiskUsageDetail;
import net.armcloud.paascenter.traffic.info.model.dto.TrafficDataDTO;
import net.armcloud.paascenter.traffic.info.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.traffic.info.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.traffic.info.redis.service.RedisService;
import net.armcloud.paascenter.traffic.info.rocketmq.support.IRocketMqProducerWrapper;
import net.armcloud.paascenter.traffic.info.service.IDeviceSystemConfigDataService;
import net.armcloud.paascenter.traffic.info.service.IPadTrafficInfoService;
import net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService;
import net.armcloud.paascenter.traffic.info.service.PadTrafficInfoSendMsgService;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class PadTrafficInfoServiceImpl implements IPadTrafficInfoService {
    @Resource
    private PadTrafficInfoMapper padTrafficInfoMapper;
    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private PadInternalFacade padInternalFacade;
    @Resource
    private DeviceInternalFacade deviceInternalFacade;
    @Resource
    private DCInternalFacade dcInternalFacade;
    @Resource
    RedisService redisService;


    @Resource
    @Qualifier("paasSqlSessionTemplate")
    private SqlSessionTemplate sqlSessionTemplate;

    @Resource
    private NetStoragePadUnitDetailMapper netStoragePadUnitDetailMapper;

    @Value("${spring.clickhouse.report.url:http://event.vmoscloud.com/open/event/pageLog}")
    private String clickHouseReportUrl;


    @Autowired
    @Qualifier("paasSqlSessionFactory")
    private SqlSessionFactory paasCenterCoreSqlSessionFactory;

    /**
     * 实例跟存储的映射永不改变,除非实例被删除
     */
    private final Cache<String, String> resUnitCodeCache = CacheBuilder.newBuilder()
            .maximumSize(100000) // 最多缓存100000条，防止 OOM
            .build();

    public String getResUnitCodeByPadCode(String containerName) {
        try {
            return resUnitCodeCache.get(containerName, () ->  netStoragePadUnitDetailMapper.selectResUnitCodeByPadCode(containerName));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    @Value("${producer-topic.cbs-traffic-disk-data}")
    private String diskTopic;

    @Value("${producer-topic.pad-traffic-info-data}")
    private String padTrafficInfoTopic;

    @Resource
    private IRocketMqProducerWrapper rocketMqProducerWrapper;

    @Resource
    private IDeviceSystemConfigDataService deviceSystemConfigDataService;

    @Resource
    private PadTrafficInfoClickHouseService padTrafficInfoClickHouseService;

    @Resource
    private PadTrafficInfoSendMsgService padTrafficInfoSendMsgService;

    @Value("${traffic.buffer.producer.disk-info.batchSize:10}")
    private int diskInfoBatchSize;

    @Value("${traffic.buffer.producer.disk-info.flushInterval:5}")
    private int diskInfoFlushIntervalSeconds;

    @Value("${traffic.buffer.producer.disk-info.capacity:1000}")
    private int diskInfoBufferCapacity;

    private SafeMemBuffer<DiskInfoDTO> diskInfoBuffer;

    @PostConstruct
    public void init() {
        log.info("初始化磁盘信息缓冲区, batchSize={}, flushInterval={}s, capacity={}",
                diskInfoBatchSize, diskInfoFlushIntervalSeconds, diskInfoBufferCapacity);

        diskInfoBuffer = SafeMemBuffer.<DiskInfoDTO>builder()
                .capacity(diskInfoBufferCapacity)
                .batchSize(diskInfoBatchSize)
                .flushInterval(Duration.ofSeconds(diskInfoFlushIntervalSeconds))
                .flushAction(this::sendDiskInfoBatch)
                .onError(e -> log.error("发送磁盘信息批次到MQ失败", e))
                .maxRetries(3)
                .initialRetryDelay(Duration.ofSeconds(1))
                .retryDelayMultiplier(2.0)
                .maxRetryDelay(Duration.ofMinutes(2))
                .deadLetterHandler(this::handleDeadLetters)
                .build();
    }

    @PreDestroy
    public void destroy() {
        if (diskInfoBuffer != null) {
            log.info("关闭磁盘信息缓冲区，最后一次刷新");
            diskInfoBuffer.close();
        }
    }

    /**
     * 将磁盘信息批量发送到MQ
     * @param diskInfoList 磁盘信息列表
     */
    private void sendDiskInfoBatch(List<DiskInfoDTO> diskInfoList) {
        if (diskInfoList == null || diskInfoList.isEmpty()) {
            return;
        }
        try {
            // 将消息列表转换为JSON数组字符串
            String jsonArrayStr = JSONUtil.toJsonStr(diskInfoList);
            rocketMqProducerWrapper.producerNormalMessage(diskTopic, null, jsonArrayStr);
            log.debug("批量发送磁盘信息到MQ成功: {} 条记录", diskInfoList.size());
        } catch (Exception e) {
            log.error("批量发送磁盘信息到MQ失败: {}", e.getMessage());
            throw e; // 重新抛出异常以触发重试机制
        }
    }

    /**
     * 处理多次重试后仍然失败的数据
     */
    private void handleDeadLetters(List<DiskInfoDTO> deadLetters) {
        log.error("磁盘信息发送到MQ多次重试失败: {} 条记录", deadLetters.size());
        try {
            // 记录失败的消息内容用于问题排查
            String deadLetterJson = JSON.toJSONString(deadLetters);
            log.error("死信内容: {}", deadLetterJson);
        } catch (Exception e) {
            log.error("处理死信失败", e);
        }
    }

    @Override
    public Boolean sendMessage(TrafficDataDTO dto) {
        try {
            PadIpDTO padIpDTO = new PadIpDTO();
            padIpDTO.setPadOutCode(dto.getPadOutCode());
            padIpDTO.setIp(dto.getPadIp());

            Pad pad = getPadByOutCodeAndIp(dto.getPadOutCode());
            if (pad == null) {
                Result<Pad> result = padInternalFacade.getPadByOutCodeAndIp(padIpDTO);
                if (Objects.isNull(result) || result.getCode() != Result.SUCCESS || Objects.isNull(result.getData())) {
                    log.error("sendMessage getPadByOutCodeAndIp result:{},dto:{}", JSONObject.toJSONString(result), dto);
                    return false;
                }
                pad = result.getData();
            }

            DcInfo dcInfo = getByPadCode(dto.getPadOutCode());
            if (dcInfo == null) {
                Result<DcInfo> result = dcInternalFacade.getByPadCode(pad.getPadCode());
                if (Objects.isNull(result) || result.getCode() != Result.SUCCESS || Objects.isNull(result.getData())) {
                    log.error("sendMessage getByPadCode result:{}", JSONObject.toJSONString(result));
                    return false;
                }
                dcInfo = result.getData();
            }


            if (ObjectUtil.isNull(pad) || ObjectUtil.isNull(dcInfo)) {
                log.error("padTrafficInfoService.handleTrafficData padIpDTO:{},pad:{},dcInfo:{}", JSON.toJSONString(padIpDTO), JSON.toJSONString(pad), JSON.toJSONString(dcInfo));
                return false;
            }
            //归属客户ID为空则不进行保存操作
            if (Objects.isNull(pad.getCustomerId())) {
                return false;
            }
            PadTrafficInfo save = new PadTrafficInfo();
            save.setPadCode(pad.getPadCode());
            save.setPadIp(pad.getPadIp());
            save.setCustomerId(pad.getCustomerId());
            save.setCreateDay(new Date());
            save.setPublicOut(dto.getPublicOut());
            save.setPublicIn(dto.getPublicIn());
            save.setPrivateOut(dto.getPrivateOut());
            save.setPrivateIn(dto.getPrivateIn());
            save.setMsgId(StringUtils.EMPTY);
            save.setTotalOut(dto.getTotalOut());
            save.setTotalIn(dto.getTotalIn());
            save.setBillingTime(dto.getBillingTime());
            save.setCreateBy("consumer");
            save.setDcCode(dcInfo.getDcCode());
            if (ThreadLocalRandom.current().nextInt(100) < 1) {
                log.info("padTrafficDataTopicConsumer 发送消息:{}", JSON.toJSONString(save));
            }
//                ClickHouseClient.sendMessage(clickHouseReportUrl,JSON.toJSONString(ReportPadTrafficInfoRequest.build(save)));
//                rocketMqProducerWrapper.producerNormalMessage(padTrafficInfoTopic,null, JSONUtil.toJsonStr(save));
//                 padTrafficInfoSendMsgService.batchSave(save);

            padTrafficInfoClickHouseService.batchSave(save);
            return this.saveTrafficInfoService(getTableName(save.getCustomerId()), save);

        } catch (Throwable e) {
            log.error("padTrafficDataTopicConsumer error落库错误", e);
            throw new RuntimeException("padTrafficDataTopicConsumer error");
        }
    }

    public Pad getPadByOutCodeAndIp(String padOutCode) {
        String key = RedisKeyPrefix.PAD_OUT_CODE_KEY + padOutCode;
        Object padObject = redisService.getCacheObject(key);
        if (ObjectUtil.isNull(padObject)) {
            return null;
        }
        return JSON.parseObject(padObject.toString(), Pad.class);

    }

    public DcInfo getByPadCode(String padCode) {
        String key = RedisKeyPrefix.DC_INFO_KEY + padCode;
        Object dcInfoObject = redisService.getCacheObject(key);
        if (ObjectUtil.isNull(dcInfoObject)) {
            return null;
        }
        return JSON.parseObject(dcInfoObject.toString(), DcInfo.class);
    }


    @Override
    public Boolean saveTrafficInfoService(String tableName,PadTrafficInfo param) {
        return padTrafficInfoMapper.insertPadTrafficInfo(tableName, param) > NumberConsts.ZERO;
    }


    @Override
    public Boolean handleTrafficData(String msgId, TrafficDataDTO dto) {
        String key = RedisKeyPrefix.PAD_TRAFFIC_INFO_MSG_LOCK + msgId;
        RLock lock = redissonDistributedLock.mustLocked(key, 1L, 30L);
        if (!redissonDistributedLock.isHeldByCurrentThread(key)) {
            return true;
        }
        try {
            PadIpDTO padIpDTO = new PadIpDTO();
            padIpDTO.setPadOutCode(dto.getPadOutCode());
            padIpDTO.setIp(dto.getPadIp());
            Result<Pad> result = padInternalFacade.getPadByOutCodeAndIp(padIpDTO);
            if (Objects.isNull(result) || Objects.isNull(result.getData())) {
                return false;
            }
            Result<DcInfo> dcInfo = dcInternalFacade.getByPadCode(result.getData().getPadCode());
            if (result.getCode() == Result.SUCCESS) {
                if (ObjectUtil.isNull(result.getData())) {
                    log.error("padTrafficInfoService.handleTrafficData msgId={} padIpDTO:{}", msgId, padIpDTO.toString());
                    return false;
                }
                //归属客户ID为空则不进行保存操作
                if (Objects.isNull(result.getData().getCustomerId())) {
                    return false;
                }
                PadTrafficInfo save = new PadTrafficInfo();
                save.setPadCode(result.getData().getPadCode());
                save.setPadIp(result.getData().getPadIp());
                save.setCustomerId(result.getData().getCustomerId());
                save.setCreateDay(new Date());
                save.setPublicOut(dto.getPublicOut());
                save.setPublicIn(dto.getPublicIn());
                save.setPrivateOut(dto.getPrivateOut());
                save.setPrivateIn(dto.getPrivateIn());
                save.setTotalOut(dto.getTotalOut());
                save.setTotalIn(dto.getTotalIn());
                save.setMsgId(msgId);
                save.setBillingTime(dto.getBillingTime());
                save.setCreateBy("consumer");
                save.setDcCode(dcInfo.getData().getDcCode());
                return this.saveTrafficInfoService(getTableName(save.getCustomerId()), save);
            } else {
                throw new RuntimeException("padTrafficDataTopicConsumer error");
            }
        } catch (Throwable e) {
            log.error("padTrafficDataTopicConsumer error落库错误", e);
            throw new RuntimeException("padTrafficDataTopicConsumer error");
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    private String getTableName(Long customerId){
        return LocalConst.DB_PAD_TRAFFIC_INFO + LocalConst.DB_TABLE_SPLIT
                + customerId;
    }

    public void reportDiskInfo(DiskInfoDTO dto) {
        // 添加到缓冲区，SafeMemBuffer会自动处理积攒和发送
        diskInfoBuffer.add(dto);
        try{
            List<DiskUsageDetail> diskUsageDetailsList = dto.getDiskUsageDetailsList();
            //更新实例使用磁盘容量
            if (!CollectionUtils.isEmpty(diskUsageDetailsList)) {
                List<NetStoragePadUnitDetail> netStoragePadUnitDetailList = diskUsageDetailsList.stream().map(diskUsageDetail -> {
                    NetStoragePadUnitDetail padUnitDetail = new NetStoragePadUnitDetail();
                    String resUnitCode = getResUnitCodeByPadCode(diskUsageDetail.getContainerName());
                    if(StringUtils.isEmpty(resUnitCode)){
                        return null;
                    }
                    padUnitDetail.setNetStorageResUnitCode(getResUnitCodeByPadCode(diskUsageDetail.getContainerName()));
                    padUnitDetail.setPadCode(diskUsageDetail.getContainerName());
                    padUnitDetail.setNetStorageResUseSize(diskUsageDetail.getUsed());
                    return padUnitDetail;
                }).filter(Objects::nonNull)  // 过滤掉 null 值
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(netStoragePadUnitDetailList)){
                    log.info("PadTrafficInfoServiceImpl.reportDiskInfo netStoragePadUnitDetailList:{}", JSON.toJSONString(netStoragePadUnitDetailList));
                    batchUpdate(netStoragePadUnitDetailList);
                }

            }
        }catch (Exception e){
            log.error("PadTrafficInfoServiceImpl.reportDiskInfo error ,e:{}",e.getMessage(), e);
        }

    }


    @Transactional // 事务注解，确保批量操作的原子性
    public void batchUpdate(List<NetStoragePadUnitDetail> list) {
            for (NetStoragePadUnitDetail item : list) {
                netStoragePadUnitDetailMapper.updateByPadCode(item);
                netStoragePadUnitDetailMapper.updateNetStorageResUnitUsedSizeByPadCode(item);
            }
    }





    public static String padSingleDigit(String input) {
        return input.length() == 1 ? "0" + input : input;
    }

    private PadTrafficInfo builtPadTrafficInfo(PadTrafficInfo pInfo){
       PadTrafficInfo padTrafficInfo = new PadTrafficInfo();
        padTrafficInfo.setBillingTime(pInfo.getBillingTime());
        padTrafficInfo.setPadCode(pInfo.getPadCode());
        padTrafficInfo.setPadIp(pInfo.getPadIp());
        padTrafficInfo.setCustomerId(pInfo.getCustomerId());
        padTrafficInfo.setCreateDay(pInfo.getCreateDay());
        padTrafficInfo.setPublicOut(pInfo.getPublicOut());
        padTrafficInfo.setPublicIn(pInfo.getPublicIn());
        padTrafficInfo.setPrivateOut(pInfo.getPrivateOut());
        padTrafficInfo.setPrivateIn(pInfo.getPrivateIn());
        padTrafficInfo.setTotalOut(pInfo.getTotalOut());
        padTrafficInfo.setTotalIn(pInfo.getTotalIn());
        padTrafficInfo.setMsgId(pInfo.getMsgId());
        padTrafficInfo.setCreateBy(pInfo.getCreateBy());
        padTrafficInfo.setDcCode(pInfo.getDcCode());
        padTrafficInfo.setCreateTime(new Date());
        return padTrafficInfo;
    }
}
