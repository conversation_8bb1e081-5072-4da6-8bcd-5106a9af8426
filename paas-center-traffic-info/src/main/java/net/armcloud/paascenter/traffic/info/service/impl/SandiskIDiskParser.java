package net.armcloud.paascenter.traffic.info.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.model.dto.InsertDiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.enums.EndianType;
import net.armcloud.paascenter.traffic.info.service.IDiskParserStrategy;
import net.armcloud.paascenter.traffic.info.util.DiskParseUtil;
import org.apache.commons.lang3.StringUtils;
@Slf4j
public class SandiskIDiskParser implements IDiskParserStrategy {

    @Override
    public boolean parse(String[] elements, InsertDiskInfoDTO dto) {
        //少于192个元素，说明数据存在问题，不做处理
        if (elements.length < 192){
            return false;
        }
        try{
            long chwdz = DiskParseUtil.parseByEndian(elements, 64, 4, EndianType.BIG_ENDIAN);
            long tlc = DiskParseUtil.parseByEndian(elements, 184, 1, EndianType.BIG_ENDIAN);
            long slc = DiskParseUtil.parseByEndian(elements, 192, 1, EndianType.BIG_ENDIAN);
            dto.setChwdz(chwdz);
            dto.setTlc(tlc);
            dto.setSlc(slc);
            return true;
        }catch(Exception e){
            log.error("解析SanDisk磁盘信息失败", e);
            return false;
        }
    }

    @Override
    public boolean supports(String cid) {
        return StringUtils.isBlank(cid) || cid.startsWith("45");
    }
}

