package net.armcloud.paascenter.traffic.info.task;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.service.ICephPressureDataService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Ceph压力数据清理定时任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CephPressureDataCleanTask {

    @Resource
    private ICephPressureDataService cephPressureDataService;

    /**
     * 数据保留天数，默认30天
     */
    @Value("${ceph.pressure.data.retention.days:30}")
    private int retentionDays;

    /**
     * 每天凌晨2点执行数据清理
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredData() {
        log.info("开始清理Ceph压力过期数据，保留天数: {}", retentionDays);
        try {
            int deletedCount = cephPressureDataService.cleanExpiredData(retentionDays);
            log.info("Ceph压力过期数据清理完成，删除{}条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理Ceph压力过期数据失败", e);
        }
    }
}
