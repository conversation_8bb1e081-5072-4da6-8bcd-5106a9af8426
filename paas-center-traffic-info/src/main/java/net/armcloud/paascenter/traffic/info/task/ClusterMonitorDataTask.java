package net.armcloud.paascenter.traffic.info.task;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.service.ICephPressureDataService;
import net.armcloud.paascenter.traffic.info.service.IClusterMonitorAggregationService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 集群监控数据处理定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ClusterMonitorDataTask {

    @Resource
    private ICephPressureDataService cephPressureDataService;

    @Resource
    private IClusterMonitorAggregationService aggregationService;

    /**
     * 每分钟执行数据聚合任务
     * 将原始数据聚合到分钟表
     */
    @Scheduled(cron = "0 * * * * ?")
    public void aggregateRawDataToMinute() {
        try {
            int aggregatedCount = aggregationService.aggregateRecentData();
            if (aggregatedCount > 0) {
                log.info("数据聚合完成，聚合{}条记录", aggregatedCount);
            }
        } catch (Exception e) {
            log.error("数据聚合任务执行失败", e);
        }
    }

    /**
     * 每天凌晨2点执行数据清理
     * 原始表保留1个月，分钟聚合表保留3个月
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredData() {
        log.info("开始清理集群监控过期数据");
        try {
            int deletedCount = cephPressureDataService.cleanExpiredData(0);
            log.info("集群监控过期数据清理完成，删除{}条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理集群监控过期数据失败", e);
        }
    }
}
