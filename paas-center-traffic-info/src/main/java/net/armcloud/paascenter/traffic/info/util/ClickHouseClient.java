package net.armcloud.paascenter.traffic.info.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 上报clickHouse日志的工具类
 */
@Slf4j
public class ClickHouseClient {

    private static final int corePoolSize = 20;
    private static final int maxPoolSize = 50;
    // 生产环境
    // private static final String URL = "http://event.vmoscloud.com/open/event/pageLog";
    // 测试
    // private static final String URL = "http://218.76.30.251:28080/open/event/pageLog";

    private static final ThreadPoolExecutor CLICK_HOUSE_CLIENT_POOL = new ThreadPoolExecutor(corePoolSize, maxPoolSize,
            5, TimeUnit.SECONDS, new LinkedBlockingQueue<>(2048), new ThreadPoolExecutor.AbortPolicy());
    private static final ScheduledExecutorService CLICK_HOUSE_SCHEDULED_EXECUTOR = Executors.newScheduledThreadPool(1); // 用于超时取消任务

    public static void sendMessage(String reportUrl, String jsonPayload) {
        try {
            // log.info("ClickHouseClient sendMessage start. jsonPayload:{}", jsonPayload);
            AtomicReference<String> atomicString = new AtomicReference<>(reportUrl);

            // 使用 CompletableFuture 异步执行任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 使用 HttpURLConnection 发送 HTTP 请求
                    String requestUrl = atomicString.get();
                    HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/json");
                    connection.setDoOutput(true); // 设置可以写请求体

                    // 发送请求体
                    try (OutputStream os = connection.getOutputStream()) {
                        byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                        os.write(input, 0, input.length);
                    }

                    // 获取响应并处理
                    try (BufferedReader br = new BufferedReader(
                            new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                        StringBuilder response = new StringBuilder();
                        String line;
                        while ((line = br.readLine()) != null) {
                            response.append(line);
                        }
                        // log.info("ClickHouseClient sendMessage success. response:{}", response.toString());
                    }


                    // 关闭连接
                    connection.disconnect();
                } catch (Exception e) {
                    log.error("ClickHouseClient send warning message error. errorMsg:{}", JSONObject.toJSONString(e),
                            e);
                }
            }, CLICK_HOUSE_CLIENT_POOL);

            // 使用 ScheduledExecutorService 在 10 秒后检查任务是否完成
            CLICK_HOUSE_SCHEDULED_EXECUTOR.schedule(() -> {
                if (!future.isDone()) {
                    log.error("ClickHouseClient sendMessage Task exceeded timeout and is being cancelled.");
                    future.cancel(true); // 超过时间后取消任务
                }
            }, 10, TimeUnit.SECONDS); // 设置超时 10 秒
        } catch (Exception e) {
            log.error("ClickHouseClient send warning message error. errorMsg:{}", JSONObject.toJSONString(e), e);
        }

    }

}
