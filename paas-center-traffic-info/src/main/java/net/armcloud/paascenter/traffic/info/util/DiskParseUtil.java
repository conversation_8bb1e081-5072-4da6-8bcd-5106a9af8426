package net.armcloud.paascenter.traffic.info.util;

import net.armcloud.paascenter.traffic.info.model.enums.EndianType;

public class DiskParseUtil {

    public static long parseByEndian(String[] elements, int offset, int length, EndianType endianType) {
        StringBuilder hexBuilder = new StringBuilder();
        if (endianType == EndianType.LITTLE_ENDIAN) {
            for (int i = 0; i < length; i++) {
                hexBuilder.insert(0, pad(elements[offset + i]));
            }
        } else {
            for (int i = length - 1; i >= 0; i--) {
                hexBuilder.append(pad(elements[offset + i]));
            }
        }
        return Long.parseUnsignedLong(hexBuilder.toString(), 16);
    }


    private static String pad(String hex) {
        return hex.length() == 1 ? "0" + hex : hex;
    }
}

