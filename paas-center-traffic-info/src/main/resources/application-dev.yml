spring:
  application:
    name: paas-center-traffic-info  #服务名称
  cloud:
    nacos:
      config:
        server-addr: ${nacos.addr:192.168.200.31}:8848 #Nacos地址
        file-extension: yaml
        namespace: armcloud-paas-${spring.profiles.active} #命名空间
        group: armcloud-paas-${spring.profiles.active} #分组
      discovery:
        server-addr: ${nacos.addr:192.168.200.31}:8848
        namespace: armcloud-paas-${spring.profiles.active}
        group: armcloud-paas-${spring.profiles.active}
  config:
    import:
      - nacos:${spring.application.name}-${spring.profiles.active}.yaml?refresh=true #Nacos配置