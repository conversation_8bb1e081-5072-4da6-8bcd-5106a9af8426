<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.clickhouse.DiskInfoMapper">

    <!-- 批量插入磁盘信息记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO armcloud.armcloud_disk_info (
        log_create_day_hour, create_time, create_date, timestamp,
        event_id, page, event_uid, device_key, version_code, version_name,
        model, brand, dc_id, device_ip, data_info, date_time,
        str_top10_cpu_info, str_top10_io_info, chwdz, tlc, slc, customer_id, customer_name,device_create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.log_create_day_hour},
            #{item.create_time},
            #{item.create_date},
            #{item.timestamp},
            #{item.event_id},
            #{item.page},
            #{item.event_uid},
            #{item.device_key},
            #{item.version_code},
            #{item.version_name},
            #{item.model},
            #{item.brand},
            #{item.dc_id},
            #{item.device_ip},
            #{item.data_info},
            #{item.date_time},
            #{item.str_top10_cpu_info},
            #{item.str_top10_io_info},
            #{item.chwdz},
            #{item.tlc},
            #{item.slc},
            #{item.customer_id},
            #{item.customer_name},
            #{item.device_create_time}
            )
        </foreach>
    </insert>
</mapper>