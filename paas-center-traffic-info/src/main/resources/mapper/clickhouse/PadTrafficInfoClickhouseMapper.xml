<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.clickhouse.PadTrafficInfoClickhouseMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO armcloud.pad_traffic_info (
        pad_code, pad_ip, customer_id, create_day, msg_id, create_by, update_by, update_time,
        billing_time, statistics_batch_hour, dc_code, public_out, public_in, private_out, private_in, total_out, total_in
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.padCode},
            #{item.padIp},
            #{item.customerId},
            #{item.createDay},
            #{item.msgId},
            #{item.createBy},
            #{item.updateBy},
            #{item.updateTime},
            #{item.billingTime},
            #{item.statisticsBatchHour},
            #{item.dcCode},
            #{item.publicOut},
            #{item.publicIn},
            #{item.privateOut},
            #{item.privateIn},
            #{item.totalOut},
            #{item.totalIn}
            )
        </foreach>
    </insert>

    <insert id="batchInsert2" parameterType="java.util.List">
        INSERT INTO armcloud.pad_traffic_info (
        pad_code, pad_ip, customer_id, create_day, msg_id, create_by, update_by, update_time,
        billing_time, statistics_batch_hour, dc_code, public_out, public_in, private_out, private_in, total_out, total_in
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.padCode},
            #{item.padIp},
            #{item.customerId},
            #{item.createDay},
            #{item.msgId},
            #{item.createBy},
            #{item.updateBy},
            #{item.updateTime},
            #{item.billingTime},
            #{item.statisticsBatchHour},
            #{item.dcCode},
            #{item.publicOut},
            #{item.publicIn},
            #{item.privateOut},
            #{item.privateIn},
            #{item.totalOut},
            #{item.totalIn}
            )
        </foreach>
    </insert>

</mapper>