<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper">

    <!-- 原始数据结果映射 -->
    <resultMap id="RawDataResultMap" type="net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorRawData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cluster_code" property="clusterCode" jdbcType="VARCHAR"/>
        <result column="metric_type" property="metricType" jdbcType="VARCHAR"/>
        <result column="metric_value" property="metricValue" jdbcType="DOUBLE"/>
        <result column="metric_unit" property="metricUnit" jdbcType="VARCHAR"/>
        <result column="report_time" property="reportTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 分钟聚合数据结果映射 -->
    <resultMap id="MinuteDataResultMap" type="net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorMinuteData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cluster_code" property="clusterCode" jdbcType="VARCHAR"/>
        <result column="metric_type" property="metricType" jdbcType="VARCHAR"/>
        <result column="minute_time" property="minuteTime" jdbcType="TIMESTAMP"/>
        <result column="avg_value" property="avgValue" jdbcType="DOUBLE"/>
        <result column="max_value" property="maxValue" jdbcType="DOUBLE"/>
        <result column="min_value" property="minValue" jdbcType="DOUBLE"/>
        <result column="data_count" property="dataCount" jdbcType="INTEGER"/>
        <result column="metric_unit" property="metricUnit" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入原始数据 -->
    <insert id="batchInsertRawData" parameterType="java.util.List">
        INSERT INTO cluster_monitor_raw_data (
            cluster_code,
            metric_type,
            metric_value,
            metric_unit,
            report_time,
            create_time,
            create_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.clusterCode},
                #{item.metricType},
                #{item.metricValue},
                #{item.metricUnit},
                #{item.reportTime},
                #{item.createTime},
                #{item.createBy}
            )
        </foreach>
    </insert>

    <!-- 批量插入分钟聚合数据 -->
    <insert id="batchInsertMinuteData" parameterType="java.util.List">
        INSERT INTO cluster_monitor_minute_data (
            cluster_code,
            metric_type,
            minute_time,
            avg_value,
            max_value,
            min_value,
            data_count,
            metric_unit,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.clusterCode},
                #{item.metricType},
                #{item.minuteTime},
                #{item.avgValue},
                #{item.maxValue},
                #{item.minValue},
                #{item.dataCount},
                #{item.metricUnit},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 从原始表查询分钟维度数据（时间跨度<=30分钟） -->
    <select id="selectMinuteDataFromRaw" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00') as time_point,
            AVG(metric_value) as avg_pressure,
            MAX(metric_value) as max_pressure,
            MIN(metric_value) as min_pressure,
            COUNT(*) as data_count,
            MAX(metric_unit) as metric_unit
        FROM cluster_monitor_raw_data
        WHERE cluster_code = #{clusterCode}
        AND metric_type = #{metricType}
        AND report_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00')
        ORDER BY time_point ASC
    </select>

    <!-- 从分钟聚合表查询数据（时间跨度>30分钟） -->
    <select id="selectMinuteDataFromAgg" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(minute_time, '%Y-%m-%d %H:%i:00') as time_point,
            avg_value as avg_pressure,
            max_value as max_pressure,
            min_value as min_pressure,
            data_count,
            metric_unit
        FROM cluster_monitor_minute_data
        WHERE cluster_code = #{clusterCode}
        AND metric_type = #{metricType}
        AND minute_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY minute_time ASC
    </select>

    <!-- 从分钟聚合表查询聚合数据（当数据量超过1000时使用） -->
    <select id="selectAggregatedDataFromAgg" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(
                DATE_SUB(minute_time, INTERVAL MINUTE(minute_time) % #{intervalMinutes} MINUTE),
                '%Y-%m-%d %H:%i:00'
            ) as time_point,
            AVG(avg_value) as avg_pressure,
            MAX(max_value) as max_pressure,
            MIN(min_value) as min_pressure,
            SUM(data_count) as data_count,
            MAX(metric_unit) as metric_unit
        FROM cluster_monitor_minute_data
        WHERE cluster_code = #{clusterCode}
        AND metric_type = #{metricType}
        AND minute_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_FORMAT(
            DATE_SUB(minute_time, INTERVAL MINUTE(minute_time) % #{intervalMinutes} MINUTE),
            '%Y-%m-%d %H:%i:00'
        )
        ORDER BY time_point ASC
    </select>

    <!-- 统计原始表中指定时间区间内的数据条数 -->
    <select id="countRawDataByTimeRange" resultType="int">
        SELECT COUNT(*)
        FROM cluster_monitor_raw_data
        WHERE cluster_code = #{clusterCode}
        AND metric_type = #{metricType}
        AND report_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 统计分钟聚合表中指定时间区间内的数据条数 -->
    <select id="countMinuteDataByTimeRange" resultType="int">
        SELECT COUNT(*)
        FROM cluster_monitor_minute_data
        WHERE cluster_code = #{clusterCode}
        AND metric_type = #{metricType}
        AND minute_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 删除原始表中指定时间之前的数据（保留1个月） -->
    <delete id="deleteRawDataBeforeTime">
        DELETE FROM cluster_monitor_raw_data
        WHERE report_time &lt; #{beforeTime}
    </delete>

    <!-- 删除分钟聚合表中指定时间之前的数据（保留3个月） -->
    <delete id="deleteMinuteDataBeforeTime">
        DELETE FROM cluster_monitor_minute_data
        WHERE minute_time &lt; #{beforeTime}
    </delete>

    <!-- 插入或更新分钟聚合数据 -->
    <insert id="insertOrUpdateMinuteData" parameterType="net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorMinuteData">
        INSERT INTO cluster_monitor_minute_data (
            cluster_code,
            metric_type,
            minute_time,
            avg_value,
            max_value,
            min_value,
            data_count,
            metric_unit,
            create_time
        ) VALUES (
            #{data.clusterCode},
            #{data.metricType},
            #{data.minuteTime},
            #{data.avgValue},
            #{data.maxValue},
            #{data.minValue},
            #{data.dataCount},
            #{data.metricUnit},
            #{data.createTime}
        ) ON DUPLICATE KEY UPDATE
            avg_value = #{data.avgValue},
            max_value = #{data.maxValue},
            min_value = #{data.minValue},
            data_count = #{data.dataCount},
            metric_unit = #{data.metricUnit},
            update_time = NOW()
    </insert>

</mapper>
