<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.paas.CephPressureDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.traffic.info.model.entity.CephPressureData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cluster_code" property="clusterCode" jdbcType="VARCHAR"/>
        <result column="ceph_pressure" property="cephPressure" jdbcType="DOUBLE"/>
        <result column="report_time" property="reportTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ceph_pressure_data (
            cluster_code,
            ceph_pressure,
            report_time,
            create_time,
            create_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.clusterCode},
                #{item.cephPressure},
                #{item.reportTime},
                #{item.createTime},
                #{item.createBy}
            )
        </foreach>
    </insert>

    <!-- 查询分钟维度数据 -->
    <select id="selectMinuteData" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00') as time_point,
            AVG(ceph_pressure) as avg_pressure,
            MAX(ceph_pressure) as max_pressure,
            MIN(ceph_pressure) as min_pressure,
            COUNT(*) as data_count
        FROM ceph_pressure_data
        WHERE cluster_code = #{clusterCode}
        AND report_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00')
        ORDER BY time_point ASC
    </select>

    <!-- 查询聚合数据 -->
    <select id="selectAggregatedData" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(
                DATE_SUB(report_time, INTERVAL MINUTE(report_time) % #{intervalMinutes} MINUTE),
                '%Y-%m-%d %H:%i:00'
            ) as time_point,
            AVG(ceph_pressure) as avg_pressure,
            MAX(ceph_pressure) as max_pressure,
            MIN(ceph_pressure) as min_pressure,
            COUNT(*) as data_count
        FROM ceph_pressure_data
        WHERE cluster_code = #{clusterCode}
        AND report_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_FORMAT(
            DATE_SUB(report_time, INTERVAL MINUTE(report_time) % #{intervalMinutes} MINUTE),
            '%Y-%m-%d %H:%i:00'
        )
        ORDER BY time_point ASC
    </select>

    <!-- 统计数据条数 -->
    <select id="countByTimeRange" resultType="int">
        SELECT COUNT(*)
        FROM ceph_pressure_data
        WHERE cluster_code = #{clusterCode}
        AND report_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 删除指定时间之前的数据 -->
    <delete id="deleteBeforeTime">
        DELETE FROM ceph_pressure_data
        WHERE report_time &lt; #{beforeTime}
    </delete>

</mapper>
