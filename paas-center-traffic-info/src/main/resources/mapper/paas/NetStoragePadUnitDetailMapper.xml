<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.paas.NetStoragePadUnitDetailMapper">
    <update id="updateByPadCode" parameterType="net.armcloud.paascenter.traffic.info.domain.entity.NetStoragePadUnitDetail">
        UPDATE net_storage_pad_unit_detail
        SET net_storage_res_use_size = #{netStorageResUseSize}
        WHERE pad_code = #{padCode}
    </update>

        <update id="updateNetStorageResUnitUsedSizeByPadCode">
            UPDATE net_storage_res_unit
            SET net_storage_res_unit_used_size = #{netStorageResUseSize}
            WHERE net_storage_res_unit_code =  #{netStorageResUnitCode}
        </update>
    <select id="selectResUnitCodeByPadCode" resultType="java.lang.String">
        SELECT net_storage_res_id FROM pad  where pad_code = #{padCode}
    </select>


</mapper>