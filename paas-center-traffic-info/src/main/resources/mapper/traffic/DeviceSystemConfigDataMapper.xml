<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.traffic.DeviceSystemConfigDataMapper">

    <insert id="insertDeviceSystemConfig" parameterType="map">
        INSERT INTO device_system_config_data_${param.tableSuffix}(`device_ip`, `device_code`, `msg_id`, `data`, `billing_time`, `create_day`, `create_by`)
        VALUES (#{param.deviceIp}, #{param.deviceCode}, #{param.msgId}, #{param.data}, #{param.billingTime}, #{param.createDay}, #{param.createBy})
    </insert>

    <select id="createTable">
        CREATE TABLE device_system_config_data_${date} (
         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
         `device_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物理机IP',
         `device_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物理机编号',
         `msg_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息ID',
         `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '打点数据',
         `billing_time` timestamp NULL DEFAULT NULL COMMENT '计费时间',
         `create_day` date NOT NULL COMMENT '创建日期',
         `create_by` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者',
         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
         `update_by` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
         `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
         PRIMARY KEY (`id`,`create_day`) USING BTREE,
         UNIQUE KEY `index_msg_id` (`msg_id`,`create_day`) USING BTREE,
         KEY `idx_device_ip` (`device_ip`) USING BTREE,
         KEY `idx_billing_time` (`billing_time`) USING BTREE,
         KEY `idx_device_code` (`device_code`)
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='板卡debian配置数据表';
    </select>
</mapper>