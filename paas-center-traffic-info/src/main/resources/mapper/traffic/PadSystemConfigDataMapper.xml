<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.traffic.PadSystemConfigDataMapper">

    <insert id="insertPadSystemConfig" parameterType="map">
        INSERT INTO pad_system_config_data_${param.tableSuffix}(`pad_code`, `pad_ip`, `msg_id`, `data`, `billing_time`, `create_day`, `create_by`)
        VALUES (#{param.padCode}, #{param.padIp}, #{param.msgId}, #{param.data}, #{param.billingTime}, #{param.createDay}, #{param.createBy})
    </insert>

    <select id="createTable">
        CREATE TABLE pad_system_config_data_${date} (
          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `pad_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '实例编码',
          `pad_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '实例IP',
          `msg_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息ID',
          `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '打点数据',
          `billing_time` timestamp NULL DEFAULT NULL COMMENT '计费时间',
          `create_day` date NOT NULL COMMENT '创建日期',
          `create_by` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者',
          `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `update_by` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
          `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`,`create_day`) USING BTREE,
          UNIQUE KEY `index_msg_id` (`msg_id`,`create_day`) USING BTREE,
          KEY `idx_pad_code` (`pad_code`) USING BTREE,
          KEY `idx_billing_time` (`billing_time`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='实例配置数据表';
    </select>
</mapper>