<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.traffic.PadTrafficInfoMapper">
    <insert id="insertPadTrafficInfo" parameterType="map">
            insert into ${tableName} (pad_code, pad_ip, customer_id, create_day, public_out, public_in,
            private_out, private_in, total_out, total_in, msg_id,
            create_by,  update_by, update_time,billing_time,dc_code)
            value (#{param.padCode}, #{param.padIp}, #{param.customerId}, #{param.createDay}, #{param.publicOut}, #{param.publicIn},
            #{param.privateOut}, #{param.privateIn}, #{param.totalOut}, #{param.totalIn}, #{param.msgId},
            #{param.createBy}, #{param.updateBy}, #{param.updateTime}, #{param.billingTime}, #{param.dcCode})
            ON DUPLICATE KEY UPDATE
            billing_time = values(billing_time)
    </insert>
</mapper>