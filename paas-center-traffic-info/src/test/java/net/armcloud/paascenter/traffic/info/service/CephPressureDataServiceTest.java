package net.armcloud.paascenter.traffic.info.service;

import net.armcloud.paascenter.traffic.info.internal.dto.CephPressureReportDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 集群监控数据服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("dev")
public class CephPressureDataServiceTest {

    @Resource
    private ICephPressureDataService cephPressureDataService;

    @Resource
    private IClusterMonitorAggregationService aggregationService;

    @Test
    public void testSaveCephPressureData() {
        // 创建测试数据
        CephPressureReportDTO reportDTO = new CephPressureReportDTO();
        reportDTO.setClusterCode("test-cluster-001");
        
        CephPressureReportDTO.Metrics metrics = new CephPressureReportDTO.Metrics();
        metrics.setCephPressure(75.5);
        reportDTO.setMetrics(metrics);

        // 保存数据
        Boolean result = cephPressureDataService.saveCephPressureData(reportDTO);
        System.out.println("保存结果: " + result);
    }

    @Test
    public void testGetChartDataShortRange() {
        // 测试短时间范围查询（<=30分钟，查询原始表）
        CephPressureQueryDTO queryDTO = new CephPressureQueryDTO();
        queryDTO.setClusterCode("test-cluster-001");
        queryDTO.setStartTime(new Date(System.currentTimeMillis() - 30 * 60 * 1000)); // 30分钟前
        queryDTO.setEndTime(new Date());

        List<CephPressureChartDTO> chartData = cephPressureDataService.getChartData(queryDTO);
        System.out.println("短时间范围查询到数据条数: " + chartData.size());

        for (CephPressureChartDTO data : chartData) {
            System.out.println("时间: " + data.getTimeStr() +
                             ", 平均压力: " + data.getAvgPressure() +
                             ", 数据点数: " + data.getDataCount());
        }
    }

    @Test
    public void testGetChartDataLongRange() {
        // 测试长时间范围查询（>30分钟，查询分钟聚合表）
        CephPressureQueryDTO queryDTO = new CephPressureQueryDTO();
        queryDTO.setClusterCode("test-cluster-001");
        queryDTO.setStartTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)); // 24小时前
        queryDTO.setEndTime(new Date());

        List<CephPressureChartDTO> chartData = cephPressureDataService.getChartData(queryDTO);
        System.out.println("长时间范围查询到数据条数: " + chartData.size());

        for (CephPressureChartDTO data : chartData) {
            System.out.println("时间: " + data.getTimeStr() +
                             ", 平均压力: " + data.getAvgPressure() +
                             ", 数据点数: " + data.getDataCount());
        }
    }

    @Test
    public void testCleanExpiredData() {
        // 清理过期数据（原始表1个月，分钟表3个月）
        int deletedCount = cephPressureDataService.cleanExpiredData(0);
        System.out.println("删除过期数据条数: " + deletedCount);
    }

    @Test
    public void testAggregateData() {
        // 测试数据聚合
        int aggregatedCount = aggregationService.aggregateRecentData();
        System.out.println("聚合数据条数: " + aggregatedCount);
    }
}
